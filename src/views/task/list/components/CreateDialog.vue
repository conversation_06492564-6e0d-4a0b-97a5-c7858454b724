<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建任务"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="选择数据类型" prop="taskType">
        <el-select
          v-model="formData.taskType"
          placeholder="请选择数据类型"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="(label, value) in TASK_TYPE_LABELS"
            :key="value"
            :label="label"
            :value="Number(value)"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择数据集" prop="taskDataId">
        <el-select
          v-model="formData.taskDataId"
          placeholder="请选择数据集"
          clearable
          style="width: 100%"
        >
          <!-- 这里需要动态加载数据集列表 -->
          <el-option label="数据集1" :value="1" />
          <el-option label="数据集2" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="选择模型" prop="aiModelId">
        <el-select
          v-model="formData.aiModelId"
          placeholder="请选择模型"
          clearable
          style="width: 100%"
        >
          <!-- 这里需要动态加载模型列表 -->
          <el-option label="模型1" value="1" />
          <el-option label="模型2" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="预警通知人员" prop="alarmNoticeUserId">
        <el-select
          v-model="formData.alarmNoticeUserId"
          placeholder="请选择预警通知人员"
          multiple
          clearable
          style="width: 100%"
        >
          <!-- 这里需要动态加载用户列表 -->
          <el-option label="用户1" value="1" />
          <el-option label="用户2" value="2" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="fillWithTestData">快速填充</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { createTask } from '@/api/alg/task'
import { TASK_TYPE_LABELS } from '../constants'

defineOptions({ name: 'CreateTaskDialog' })

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive({
  taskType: undefined,
  taskDataId: undefined,
  aiModelId: '',
  alarmNoticeUserId: []
})

const formRules = {
  taskType: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
  taskDataId: [{ required: true, message: '请选择数据集', trigger: 'change' }],
  aiModelId: [{ required: true, message: '请选择模型', trigger: 'change' }],
  alarmNoticeUserId: [{ required: true, message: '请选择预警通知人员', trigger: 'change' }]
}

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
  resetForm()
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  Object.assign(formData, {
    taskType: undefined,
    taskDataId: undefined,
    aiModelId: '',
    alarmNoticeUserId: []
  })
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  const isValid = await formRef.value.validate().catch(() => false)
  if (!isValid) return

  loading.value = true
  try {
    const payload = {
      taskType: formData.taskType!,
      taskDataId: formData.taskDataId!,
      aiModelId: formData.aiModelId!,
      ...(formData.alarmNoticeUserId.length > 0 && {
        alarmNoticeUserId: formData.alarmNoticeUserId.join(',')
      })
    }

    await createTask(payload)
    ElMessage.success('创建成功')
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error('创建失败')
    console.error('创建任务失败:', error)
  } finally {
    loading.value = false
  }
}

const fillWithTestData = () => {
  formData.taskType = 0
  formData.taskDataId = 1
  formData.aiModelId = '1'
  formData.alarmNoticeUserId = ['1']
}

defineExpose({
  open
})
</script>

<style scoped>
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
