import { ref, reactive } from 'vue'
import { getDevicePage, deleteDevice } from '@/api/alg/device'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ResourceDevice, QueryParams } from './types'

export function useList() {
  const loading = ref(true)
  const deviceList = ref<ResourceDevice[]>([])
  const total = ref(0)

  const queryParams = reactive<QueryParams>({
    pageNo: 1,
    pageSize: 10,
    deviceCode: undefined,
    deviceName: undefined,
    ip: undefined,
    username: undefined,
    status: undefined,
    createTime: undefined
  })

  /** 查询列表 */
  const getList = async () => {
    loading.value = true
    try {
      const data = await getDevicePage(queryParams)
      deviceList.value = data.list
      total.value = data.total
    } catch (error) {
      ElMessage.error('获取视频流设备列表失败')
      console.error('获取视频流设备列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 查看设备操作 */
  const handleView = (row: ResourceDevice) => {
    ElMessage.info('查看功能开发中...')
  }

  /** 编辑设备操作 */
  const handleEdit = (row: ResourceDevice) => {
    ElMessage.info('编辑功能开发中...')
  }

  /** 删除设备操作 */
  const handleDelete = async (row: ResourceDevice) => {
    try {
      await ElMessageBox.confirm(`确认删除设备"${row.deviceName}"?`, '提示', {
        type: 'warning',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消'
      })
      await deleteDevice({ id: row.id })
      ElMessage.success('删除成功')
      await getList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
        console.error('删除设备失败:', error)
      }
    }
  }

  return {
    loading,
    deviceList,
    total,
    queryParams,
    getList,
    handleQuery,
    handleView,
    handleEdit,
    handleDelete
  }
}
