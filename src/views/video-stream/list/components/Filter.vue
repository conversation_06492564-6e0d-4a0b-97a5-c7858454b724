<template>
  <el-form :model="localParams" ref="queryFormRef" :inline="true">
    <el-form-item label="设备编号" prop="deviceCode">
      <el-input
        v-model="localParams.deviceCode"
        placeholder="请输入设备编号"
        clearable
        class="!w-160px"
      />
    </el-form-item>
    <el-form-item label="设备名称" prop="deviceName">
      <el-input
        v-model="localParams.deviceName"
        placeholder="请输入设备名称"
        clearable
        class="!w-160px"
      />
    </el-form-item>
    <el-form-item label="IP地址" prop="ip">
      <el-input v-model="localParams.ip" placeholder="请输入IP地址" clearable class="!w-140px" />
    </el-form-item>
    <el-form-item label="用户名" prop="username">
      <el-input
        v-model="localParams.username"
        placeholder="请输入用户名"
        clearable
        class="!w-140px"
      />
    </el-form-item>
    <el-form-item label="设备状态" prop="status">
      <el-select
        v-model="localParams.status"
        placeholder="请选择设备状态"
        clearable
        class="!w-140px"
      >
        <el-option label="离线" :value="0" />
        <el-option label="在线" :value="10" />
      </el-select>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-date-picker
        v-model="localParams.createTime"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="!w-280px"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { QueryParams } from '../types'

const props = defineProps<{
  queryParams: QueryParams
}>()

const emit = defineEmits<{
  query: []
  reset: []
}>()

const queryFormRef = ref()
const localParams = reactive({ ...props.queryParams })

watch(
  () => props.queryParams,
  (newVal) => {
    Object.assign(localParams, newVal)
  },
  { deep: true }
)

const handleQuery = () => {
  Object.assign(props.queryParams, localParams)
  emit('query')
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  emit('reset')
}
</script>
