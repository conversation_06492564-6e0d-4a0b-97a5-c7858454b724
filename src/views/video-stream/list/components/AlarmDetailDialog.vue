<template>
  <el-dialog
    v-model="dialogVisible"
    title="告警详情"
    width="800px"
    :before-close="handleClose"
    append-to-body
    class="alarm-detail-dialog"
  >
    <!-- 告警截图 -->
    <div class="alarm-image-container">
      <el-image
        :src="alarmData.alarmFileUrl"
        fit="contain"
        class="alarm-image"
        :preview-src-list="[alarmData.alarmFileUrl]"
      />
    </div>

    <!-- 告警信息 -->
    <div class="alarm-info">
      <div class="info-item">
        <span class="info-label">视频时间点：</span>
        <span class="info-value">{{ alarmData.alarmTime || '01:21:12' }}</span>
      </div>

      <div class="info-item">
        <span class="info-label">告警原因：</span>
        <span class="info-value">{{ alarmData.alarmTypeName || '未按规定佩戴安全帽' }}</span>
      </div>

      <div class="info-item">
        <span class="info-label">处理结果：</span>
        <el-select
          v-model="formData.processResult"
          placeholder="请选择处理结果"
          class="process-select"
        >
          <el-option label="已处理" value="processed" />
          <el-option label="误报" value="false_alarm" />
          <el-option label="待处理" value="pending" />
        </el-select>
      </div>

      <div class="info-item">
        <span class="info-label">备注：</span>
        <el-input
          v-model="formData.remark"
          type="textarea"
          placeholder="请填写备注内容"
          :rows="3"
          class="remark-input"
        />
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { AlarmDetails } from '../types'

defineOptions({ name: 'AlarmDetailDialog' })

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const loading = ref(false)

const alarmData = ref<Partial<AlarmDetails>>({})

const formData = reactive({
  processResult: '',
  remark: ''
})

/** 打开弹窗 */
const open = (data: AlarmDetails) => {
  alarmData.value = data
  dialogVisible.value = true
  resetForm()
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  formData.processResult = ''
  formData.remark = ''
}

/** 确认处理 */
const handleConfirm = async () => {
  if (!formData.processResult) {
    ElMessage.warning('请选择处理结果')
    return
  }

  loading.value = true
  try {
    // TODO: 调用处理告警的API
    // await processAlarm({
    //   id: alarmData.value.id,
    //   processResult: formData.processResult,
    //   remark: formData.remark
    // })

    ElMessage.success('处理成功')
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error('处理失败')
    console.error('处理告警失败:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
.alarm-image-container {
  margin: 20px 0;
  text-align: center;
}

.alarm-image {
  width: 100%;
  max-height: 360px;
  border-radius: 4px;
}

.alarm-info {
  padding: 0 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  flex-shrink: 0;
  font-size: 14px;
  color: #8f959e;
  line-height: 32px;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #1f2329;
  line-height: 32px;
}

.process-select {
  width: 320px;
}

.remark-input {
  width: 320px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 20px 24px;
}
</style>
