// 视频流设备相关类型定义
export interface ResourceDevice {
  id: number
  deviceCode: string
  deviceName: string
  ip: string
  port: number
  description: string
  username: string
  password: string
  channel: string
  liveUrl: string
  status: number // 0-离线，10-在线
  createTime: string
  creator: number
  updateTime: string
  updater: string
  deleted: boolean
  deptId: number
  tenantId: number
}

export interface QueryParams {
  pageNo: number
  pageSize: number
  deviceCode?: string
  deviceName?: string
  ip?: string
  username?: string
  status?: number
  createTime?: string[]
}
