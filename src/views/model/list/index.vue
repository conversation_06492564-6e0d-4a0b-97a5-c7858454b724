<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container mb-3">
      <Filter :query-params="queryParams" @query="handleQuery" @reset="resetQuery" />
    </div>

    <!-- 数据列表 -->
    <div class="data-list-container">
      <div class="card-header-container">
        <h5 class="card-title">算法模型</h5>
        <el-button type="primary" @click="handleCreate">新建模型</el-button>
      </div>
      <div class="grid-wrapper">
        <div class="card-grid" v-loading="loading">
          <Card
            v-for="item in modelList"
            :key="item.id"
            :model="item"
            @update="handleUpdate(item)"
            @delete="handleDelete(item)"
          />
        </div>
      </div>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="PAGE_SIZES as number[]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>

    <!-- 创建模型弹窗 -->
    <CreateDialog ref="createDialogRef" @success="handleCreateSuccess" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Card from './components/Card.vue'
import Filter from './components/Filter.vue'
import CreateDialog from './components/CreateDialog.vue'
import { useList } from './useList'
import { PAGE_SIZES } from './constants'

defineOptions({ name: 'Model' })

const createDialogRef = ref()

const {
  loading,
  total,
  modelList,
  queryParams,
  getList,
  handleQuery,
  handleUpdate,
  handleDelete
} = useList()

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.modelName = undefined
  queryParams.status = undefined
  handleQuery()
}

/** 新建按钮操作 */
const handleCreate = () => {
  createDialogRef.value?.open()
}

/** 创建成功回调 */
const handleCreateSuccess = () => {
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.data-list-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  padding: 14px 20px;
  height: calc(100vh - 158px);
  box-sizing: border-box;
}
.grid-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-top: 14px;
}
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(256px, 1fr));
  gap: 20px;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
.filter-container {
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  padding-left: 20px;
  display: flex;
  align-items: center;
  height: 72px;
}
.card-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
