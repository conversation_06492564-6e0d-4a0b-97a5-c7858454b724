import { MODEL_STATUS } from './constants'

export type ModelStatus = (typeof MODEL_STATUS)[keyof typeof MODEL_STATUS]

export interface Model {
  id: number
  modelName: string
  modelFileId: string
  modelFileUrl: string
  modelVideoFileId: string
  modelVideoFileUrl: string
  coverImageFileId: number
  coverImageFileUrl: string
  status: ModelStatus
  createTime: string
  creator: string
  updater: string
  deleted: boolean
  tenantId: number
}

export interface QueryParams {
  pageNo: number
  pageSize: number
  modelName?: string
  status?: ModelStatus
}

export interface CreateModelForm {
  modelName: string
  modelFileId: string
  coverImageFileId?: number
  modelVideoFileId?: string
  status: ModelStatus | undefined
  alarmType: string | undefined
}
