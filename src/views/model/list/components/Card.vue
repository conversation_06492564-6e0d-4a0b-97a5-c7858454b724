<template>
  <div class="model-card" @click="handleDetail">
    <img class="card-image" :src="model.coverImageFileUrl || DEFAULT_COVER_URL" />
    <span class="card-title">{{ model.modelName }}</span>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import type { Model } from '../types'
import { DEFAULT_COVER_URL } from '../constants'

const props = defineProps<{
  model: Model
}>()

const router = useRouter()
const handleDetail = () => {
  // TODO: 跳转到模型详情页
  // router.push({ name: 'ModelDetail', query: { id: props.model.id } })
}
</script>

<style scoped>
.model-card {
  position: relative;
  width: 256px;
  height: 251px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  cursor: pointer;
}

.card-image {
  width: 100%;
  height: 160px;
  border-radius: 4px;
  object-fit: cover;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  color: #1f2329;
  white-space: pre;
  margin: 16px 0 12px;
}

.card-meta-row {
  display: flex;
  width: 100%;
}

.card-meta-row + .card-meta-row {
  margin-top: 12px;
}

.meta-item {
  flex: 0 0 50%;
  line-height: 16px;
  white-space: pre;
}

.meta-label,
.meta-value {
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
}

.meta-label {
  color: #8f959e;
}

.meta-value {
  color: #1f2329;
}
</style>
