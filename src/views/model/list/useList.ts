import { ref, reactive } from 'vue'
import { getModelPage, deleteModel } from '@/api/alg/model'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Model, QueryParams } from './types'

export function useList() {
  const loading = ref(true)
  const modelList = ref<Model[]>([])
  const total = ref(0)

  const queryParams = reactive<QueryParams>({
    pageNo: 1,
    pageSize: 10,
    modelName: undefined,
    status: undefined
  })

  /** 查询列表 */
  const getList = async () => {
    loading.value = true
    try {
      const data = await getModelPage(queryParams)
      modelList.value = data.list
      total.value = data.total
    } catch (error) {
      ElMessage.error('获取模型列表失败')
      console.error('获取模型列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 更新按钮操作 */
  const handleUpdate = (_row: Model) => {
    // TODO: 跳转到编辑页面或打开编辑弹窗
    ElMessage.info('编辑功能开发中...')
  }

  /** 删除按钮操作 */
  const handleDelete = async (row: Model) => {
    try {
      await ElMessageBox.confirm(`确认删除模型"${row.modelName}"?`, '提示', {
        type: 'warning'
      })
      await deleteModel({ id: row.id })
      ElMessage.success('删除成功')
      await getList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
        console.error('删除模型失败:', error)
      }
    }
  }

  return {
    loading,
    modelList,
    total,
    queryParams,
    getList,
    handleQuery,
    handleUpdate,
    handleDelete
  }
}
