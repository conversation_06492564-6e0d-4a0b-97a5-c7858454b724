import { ref, reactive } from 'vue'
import { getDatasetPage, deleteDataset } from '@/api/alg/dataset'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Dataset, QueryParams } from './types'

export function useList() {
  const loading = ref(true)
  const datasetList = ref<Dataset[]>([])
  const total = ref(0)

  const queryParams = reactive<QueryParams>({
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    type: undefined
  })

  /** 查询列表 */
  const getList = async () => {
    loading.value = true
    try {
      const data = await getDatasetPage(queryParams)
      datasetList.value = data.list
      total.value = data.total
    } catch (error) {
      ElMessage.error('获取数据集列表失败')
      console.error('获取数据集列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1
    getList()
  }

  /** 更新按钮操作 */
  const handleUpdate = (_row: Dataset) => {
    // TODO: 跳转到编辑页面或打开编辑弹窗
    ElMessage.info('编辑功能开发中...')
  }

  /** 删除按钮操作 */
  const handleDelete = async (row: Dataset) => {
    try {
      await ElMessageBox.confirm(`确认删除数据集"${row.name}"?`, '提示', {
        type: 'warning'
      })
      await deleteDataset({ id: row.id })
      ElMessage.success('删除成功')
      await getList()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
        console.error('删除数据集失败:', error)
      }
    }
  }

  return {
    loading,
    datasetList,
    total,
    queryParams,
    getList,
    handleQuery,
    handleUpdate,
    handleDelete
  }
}
