<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建数据集"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="数据集名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入数据集名称"
          clearable
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="数据集类型" prop="type">
        <el-select
          v-model="formData.type"
          placeholder="请选择数据集类型"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="(label, value) in DATASET_TYPE_LABELS"
            :key="value"
            :label="label"
            :value="Number(value)"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="上传封面图" prop="coverImageFileId">
        <UploadImgDetail
          v-model="formData.coverUrl"
          :file-size="5"
          :file-type="['image/jpeg', 'image/png']"
          width="120px"
          height="120px"
          :upload-api="handleUpload"
          @upload-success="handleUploadSuccess"
        >
          <template #tip>
            <div class="upload-tip"> 支持jpg/png格式，不超过5m </div>
          </template>
        </UploadImgDetail>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="fillWithTestData">快速填充</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { UploadImgDetail } from '@/components/UploadFile'
import { createDataset } from '@/api/alg/dataset'
import { uploadFileDetail } from '@/api/infra/file'
import { DATASET_TYPE_LABELS } from '../constants'
import type { CreateDatasetForm } from '../types'

defineOptions({ name: 'CreateDatasetDialog' })

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

const formData = reactive<CreateDatasetForm>({
  name: '',
  type: undefined,
  coverUrl: ''
})
let coverImageFileId: number | undefined

const formRules: FormRules<CreateDatasetForm> = {
  name: [
    { required: true, message: '请输入数据集名称', trigger: 'blur' },
    { min: 1, max: 50, message: '数据集名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择数据集类型', trigger: 'change' }]
}

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
  resetForm()
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: undefined,
    coverUrl: ''
  })
  coverImageFileId = undefined
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  const isValid = await formRef.value.validate().catch(() => false)
  if (!isValid) return

  loading.value = true
  try {
    const payload = {
      name: formData.name,
      type: formData.type!,
      ...(coverImageFileId && { coverImageFileId })
    }

    await createDataset(payload)
    ElMessage.success('创建成功')
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error('创建失败')
    console.error('创建数据集失败:', error)
  } finally {
    loading.value = false
  }
}

const handleUpload = async (file: File) => {
  const res = await uploadFileDetail({ file })
  ElMessage.success('上传成功')
  return res
}

const handleUploadSuccess = (res: any) => {
  formData.coverUrl = res.data.url
  coverImageFileId = res.data.id
}

const fillWithTestData = () => {
  formData.name = '测试数据集 ' + new Date().getTime()
  formData.type = 1
}

defineExpose({
  open
})
</script>

<style scoped>
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
