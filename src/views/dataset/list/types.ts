import { DATASET_TYPES } from './constants'

export type DatasetType = (typeof DATASET_TYPES)[keyof typeof DATASET_TYPES]

export interface Dataset {
  id: number
  name: string
  type: DatasetType
  dataSourceSize: number
  dataSourceNum: number
  creatorName: string
  coverImageFileUrl: string
  status: number
  markFlag: number
  createTime: string
  creator: string
  updater: string
  deleted: string
  datasetFiles: any[] // 根据需要定义更具体的类型
  coverImageFileId: number
}

export interface QueryParams {
  pageNo: number
  pageSize: number
  name?: string
  type?: DatasetType
}

export interface CreateDatasetForm {
  name: string
  type: DatasetType | undefined
  coverUrl?: string
}
