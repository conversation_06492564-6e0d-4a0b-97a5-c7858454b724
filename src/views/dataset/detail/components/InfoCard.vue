<template>
  <div class="dataset-info-card card">
    <img class="dataset-cover" :src="dataset?.coverImageFileUrl" />
    <div class="dataset-info">
      <h3 class="dataset-name">{{ dataset?.name }}</h3>
      <p><span>类型：</span><span>{{ dataset?.type }}</span></p>
      <p><span>数量：</span><span>{{ dataset?.dataSourceNum }}</span></p>
      <p><span>大小：</span><span>{{ dataset?.dataSourceSize }} Mb</span></p>
      <p><span>用户：</span><span>{{ dataset?.creatorName }}</span></p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Dataset } from '../../list/types'

defineOptions({ name: 'DatasetInfoCard' })

interface Props {
  dataset?: Dataset
}

defineProps<Props>()
</script>

<style scoped>
.card {
  background-color: #fff;
  border-radius: 6px;
  padding: 20px;
}

.dataset-info-card {
  display: flex;
  gap: 24px;
}

.dataset-cover {
  width: 256px;
  height: 160px;
  border-radius: 4px;
  object-fit: cover;
}

.dataset-info h3 {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 500;
}

.dataset-info p {
  margin: 0 0 12px;
  font-size: 14px;
  color: #1f2329;
}

.dataset-info p:last-child {
  margin-bottom: 0;
}

.dataset-info p span:first-child {
  color: #8f959e;
}
</style>
