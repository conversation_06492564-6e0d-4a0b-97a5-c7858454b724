<template>
  <div class="file-list-card card">
    <div class="file-list-header">
      <h3 class="card-title">文件列表</h3>
      <div class="header-actions">
        <span class="upload-tips">支持JPG/JPEG/GIF/PNG/PDF格式，不超过5M</span>
        <el-button type="primary" @click="handleUpload">上传文件</el-button>
      </div>
    </div>
    <div class="file-grid-wrapper">
      <div v-if="fileImages.length > 0" class="file-grid">
        <img v-for="(image, index) in fileImages" :key="index" class="file-item" :src="image" />
      </div>
      <div v-else class="empty-state">
        <div class="empty-icon">📁</div>
        <div class="empty-text">暂无文件</div>
        <div class="empty-desc">点击上传文件按钮添加文件</div>
      </div>
    </div>
    <div class="pagination-container">
      <el-pagination
        :current-page="queryParams.pageNo"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface QueryParams {
  pageNo: number
  pageSize: number
}

interface Props {
  queryParams: QueryParams
  total: number
  fileImages: string[]
}

interface Emits {
  (e: 'upload'): void
  (e: 'page-change', params: QueryParams): void
}

defineOptions({ name: 'DatasetFileManager' })

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleUpload = () => {
  emit('upload')
}

const handleSizeChange = (size: number) => {
  emit('page-change', { pageNo: 1, pageSize: size })
}

const handleCurrentChange = (page: number) => {
  emit('page-change', { pageNo: page, pageSize: props.queryParams.pageSize })
}
</script>

<style scoped>
.card {
  background-color: #fff;
  border-radius: 6px;
  padding: 20px;
}

.file-list-card {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 13px;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.upload-tips {
  font-size: 14px;
  color: #8f959e;
}

.file-grid-wrapper {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(256px, 1fr));
  gap: 20px;
}

.file-item {
  width: 100%;
  height: 160px;
  border-radius: 4px;
  object-fit: cover;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #8f959e;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #1f2329;
}

.empty-desc {
  font-size: 14px;
}
</style>
