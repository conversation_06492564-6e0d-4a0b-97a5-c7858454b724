<template>
  <div class="dataset-detail-container app-container">
    <!-- Breadcrumb -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/dataset' }">数据集</el-breadcrumb-item>
      <el-breadcrumb-item>数据集明细</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- Dataset Info -->
    <InfoCard :dataset="dataset" />

    <!-- File List -->
    <FileManager
      :query-params="queryParams"
      :total="total"
      :file-images="fileImages"
      @upload="handleUpload"
      @page-change="handlePageChange"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import * as DatasetApi from '@/api/alg/dataset'
import type { Dataset } from '../list/types'
import InfoCard from './components/InfoCard.vue'
import FileManager from './components/FileManager.vue'

defineOptions({ name: 'DatasetDetail' })

const route = useRoute()
const datasetId = Number(route.query.id)
const dataset = ref<Dataset>()

const queryParams = reactive({
  pageNo: 1,
  pageSize: 20
})
const total = ref(0)
const fileImages = ref([])

const getDatasetDetail = async () => {
  if (datasetId) {
    const data = await DatasetApi.getDataset({ id: datasetId })
    dataset.value = data
    // TODO: fetch file list
  }
}

const handleUpload = () => {
  // TODO: 实现文件上传逻辑
  console.log('上传文件')
}

const handlePageChange = (params: { pageNo: number; pageSize: number }) => {
  queryParams.pageNo = params.pageNo
  queryParams.pageSize = params.pageSize
  // TODO: 重新获取文件列表
  console.log('分页变化:', params)
}

onMounted(() => {
  getDatasetDetail()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 74px);
  gap: 12px;
}
</style>
