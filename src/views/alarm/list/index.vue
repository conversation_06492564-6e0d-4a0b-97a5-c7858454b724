<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container mb-3">
      <Filter :query-params="queryParams" @query="handleQuery" @reset="resetQuery" />
    </div>

    <!-- 数据列表 -->
    <div class="data-list-container">
      <div class="card-header-container">
        <h5 class="card-title">告警明细列表</h5>
      </div>

      <!-- 表格 -->
      <div class="table-wrapper">
        <el-table
          :data="alarmDetailsList"
          v-loading="loading"
          style="width: 100%"
          :header-cell-style="{ backgroundColor: '#f2f3f5' }"
        >
          <el-table-column prop="alarmTypeName" label="告警名称" min-width="120" />
          <el-table-column prop="alarmTypeName" label="告警原因" min-width="120" />
          <el-table-column prop="status" label="告警状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getAlarmStatusColor(row.status)" size="small">
                {{ getAlarmStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="alarmFileUrl" label="告警照片" width="80" align="center">
            <template #default="{ row }">
              <el-button
                v-if="row.alarmFileUrl"
                link
                type="primary"
                size="small"
                @click="previewImage(row.alarmFileUrl)"
              >
                查看照片
              </el-button>
              <span v-else class="text-gray-400">无照片</span>
            </template>
          </el-table-column>
          <el-table-column prop="noticeUsers" label="预警通知" width="100">
            <template #default="{ row }">
              <span style="color: #409eff">{{ row.noticeUsers || '张三, 李四' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="processResult" label="处理结果" width="100">
            <template #default="{ row }">
              <el-tag :type="getProcessResultColor(row.processResult)" size="small">
                {{ getProcessResultLabel(row.processResult) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button link type="danger" @click="handleClear(row)">清除</el-button>
              <el-button link type="primary" @click="handleProcess(row, alarmDetailDialogRef)"
                >处理</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="PAGE_SIZES as number[]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>

    <!-- 告警详情弹窗 -->
    <AlarmDetailDialog ref="alarmDetailDialogRef" @success="handleDetailSuccess" />

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImageViewer"
      :url-list="[previewImageUrl]"
      @close="closeImageViewer"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Filter from './components/Filter.vue'
import AlarmDetailDialog from './components/AlarmDetailDialog.vue'
import { useList } from './useList'
import { PAGE_SIZES } from './constants'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'AlarmDetails' })

const alarmDetailDialogRef = ref()
const showImageViewer = ref(false)
const previewImageUrl = ref('')

const {
  loading,
  total,
  alarmDetailsList,
  queryParams,
  getList,
  handleQuery,
  handleProcess,
  handleClear
} = useList()

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.taskId = undefined
  queryParams.alarmTypeName = undefined
  queryParams.status = undefined
  queryParams.processResult = undefined
  queryParams.createTime = undefined
  handleQuery()
}

/** 获取告警状态标签 */
const getAlarmStatusLabel = (status: number) => {
  const statusMap = {
    0: '待处理',
    1: '处理中',
    2: '已处理',
    3: '已忽略'
  }
  return statusMap[status] || '未知'
}

/** 获取告警状态颜色 */
const getAlarmStatusColor = (status: number) => {
  const colorMap = {
    0: 'warning',
    1: 'primary',
    2: 'success',
    3: 'info'
  }
  return colorMap[status] || 'info'
}

/** 获取处理结果标签 */
const getProcessResultLabel = (result: string) => {
  const resultMap = {
    processed: '已处理',
    false_alarm: '误报',
    pending: '待处理',
    ignored: '已忽略'
  }
  return resultMap[result] || '待处理'
}

/** 获取处理结果颜色 */
const getProcessResultColor = (result: string) => {
  const colorMap = {
    processed: 'success',
    false_alarm: 'warning',
    pending: 'danger',
    ignored: 'info'
  }
  return colorMap[result] || 'danger'
}

/** 详情弹窗成功回调 */
const handleDetailSuccess = () => {
  getList()
}

/** 预览图片 */
const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl
  showImageViewer.value = true
}

/** 关闭图片预览 */
const closeImageViewer = () => {
  showImageViewer.value = false
  previewImageUrl.value = ''
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.data-list-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  padding: 14px 20px;
  height: calc(100vh - 158px);
  box-sizing: border-box;
}
.table-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-top: 14px;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
.filter-container {
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 0;
}
.card-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
