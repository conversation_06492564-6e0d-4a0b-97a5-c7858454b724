<template>
  <el-form :model="localParams" ref="queryFormRef" :inline="true">
    <el-form-item label="告警类型" prop="alarmTypeName">
      <el-select
        v-model="localParams.alarmTypeName"
        placeholder="请选择告警类型"
        clearable
        class="!w-160px"
      >
        <el-option label="安全帽识别" value="安全帽识别" />
        <el-option label="反光衣识别" value="反光衣识别" />
        <el-option label="烟火识别" value="烟火识别" />
        <el-option label="人员入侵" value="人员入侵" />
        <el-option label="车辆识别" value="车辆识别" />
      </el-select>
    </el-form-item>
    <el-form-item label="告警状态" prop="status">
      <el-select
        v-model="localParams.status"
        placeholder="请选择告警状态"
        clearable
        class="!w-140px"
      >
        <el-option label="待处理" :value="0" />
        <el-option label="处理中" :value="1" />
        <el-option label="已处理" :value="2" />
        <el-option label="已忽略" :value="3" />
      </el-select>
    </el-form-item>
    <el-form-item label="处理结果" prop="processResult">
      <el-select
        v-model="localParams.processResult"
        placeholder="请选择处理结果"
        clearable
        class="!w-140px"
      >
        <el-option label="已处理" value="processed" />
        <el-option label="误报" value="false_alarm" />
        <el-option label="待处理" value="pending" />
        <el-option label="已忽略" value="ignored" />
      </el-select>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-date-picker
        v-model="localParams.createTime"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="!w-280px"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { QueryParams } from '../types'

const props = defineProps<{
  queryParams: QueryParams
}>()

const emit = defineEmits<{
  query: []
  reset: []
}>()

const queryFormRef = ref()
const localParams = reactive({ ...props.queryParams })

watch(
  () => props.queryParams,
  (newVal) => {
    Object.assign(localParams, newVal)
  },
  { deep: true }
)

const handleQuery = () => {
  Object.assign(props.queryParams, localParams)
  emit('query')
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  emit('reset')
}
</script>
