---
title: alg项目
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# alg项目

Base URLs:

# Authentication

# 管理后台 - 认证

<a id="opIdlogin"></a>

## POST 使用账号密码登录

POST /admin-api/system/auth/login

> Body 请求参数

```json
{
  "captchaVerification": "PfcH6mgr8tpXuMWFjvW6YVaqrswIuwmWI5dsVZSg7sGpWtDCUbHuDEXl3cFB1+VvCC/rAkSwK8Fad52FSuncVg==",
  "username": "yudaoyuan<PERSON>",
  "password": "buzhidao",
  "socialType": 10,
  "socialCode": 1024,
  "socialState": "9b2ffbc1-7425-4155-9894-9d5c08541d62",
  "socialCodeValid": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|Authorization|header|string| 否 |none|
|body|body|[AuthLoginReqVO](#schemaauthloginreqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":{"userId":1024,"accessToken":"happy","refreshToken":"nice","expiresTime":"2019-08-24T14:15:22Z"},"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultAuthLoginRespVO](#schemacommonresultauthloginrespvo)|

# 管理后台 - 模型

## POST 创建模型

POST /alg/ai-model/create

创建模型

> Body 请求参数

```json
{
  "id": 24150,
  "modelName": "张三",
  "modelFileId": "14429",
  "modelVideoFileId": "19647",
  "coverImageFileId": 14429,
  "status": 0,
  "createTime": "string",
  "creator": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "tenantId": 17682
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AiModelSaveReqVO](#schemaaimodelsavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## PUT 更新模型

PUT /alg/ai-model/update

更新模型

> Body 请求参数

```json
{
  "id": 24150,
  "modelName": "张三",
  "modelFileId": "14429",
  "modelVideoFileId": "19647",
  "coverImageFileId": 14429,
  "status": 0,
  "createTime": "string",
  "creator": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "tenantId": 17682
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AiModelUpdateReqVO](#schemaaimodelupdatereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 删除模型

DELETE /alg/ai-model/delete

删除模型

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 批量删除模型

DELETE /alg/ai-model/delete-list

批量删除模型

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获得模型

GET /alg/ai-model/get

获得模型

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "modelName": "",
    "modelFileId": "",
    "modelImageFileId": "",
    "modelVideoFileId": "",
    "status": 0,
    "createTime": "",
    "creator": "",
    "updateTime": "",
    "updater": "",
    "deleted": false,
    "tenantId": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAiModelRespVO](#schemacommonresultaimodelrespvo)|

## GET 获得模型分页

GET /alg/ai-model/page

获得模型分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |ID|
|modelName|query|string| 否 |模型名称|
|modelFileId|query|string| 否 |关联的模型文件ID|
|coverImageFileId|query|integer(int64)| 否 |模型封面图片文件id|
|modelVideoFileId|query|string| 否 |模型宣传视频文件id|
|status|query|integer| 否 |启用状态：0-停用，1-启用|
|createTime|query|array[string]| 否 |创建时间|
|creator|query|string| 否 |创建人|
|updateTime|query|array[string]| 否 |更新时间|
|updater|query|string| 否 |更新人|
|deleted|query|boolean| 否 |0代表存在 2代表删除|
|tenantId|query|integer(int64)| 否 |租户编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 24150,
        "modelName": "模型名称",
        "modelFileId": "1899",
        "modelFileUrl": "http://125.74.131.242:9000/alg-prod/20250730/DD2025070300001 (2)_1753869046734.png",
        "modelVideoFileId": "1905",
        "modelVideoFileUrl": "http://125.74.131.242:9000/alg-prod/20250731/888f48310275899a449f79b9e5a41e78_1753897027966.mp4",
        "status": 0,
        "createTime": 1753791490000,
        "creator": "1",
        "updateTime": 1753791490000,
        "updater": "1",
        "deleted": false,
        "tenantId": 1
      },
      {
        "id": "1946760071329886211",
        "modelName": "张三",
        "modelFileId": "1899",
        "modelFileUrl": "http://125.74.131.242:9000/alg-prod/20250730/DD2025070300001 (2)_1753869046734.png",
        "modelVideoFileId": "1905",
        "modelVideoFileUrl": "http://125.74.131.242:9000/alg-prod/20250731/888f48310275899a449f79b9e5a41e78_1753897027966.mp4",
        "status": 2,
        "createTime": 1753893072000,
        "creator": "1",
        "updateTime": 1753893072000,
        "updater": "1",
        "deleted": false,
        "tenantId": 1
      },
      {
        "id": "1946760071329886212",
        "modelName": "张三",
        "modelFileId": "1899",
        "modelFileUrl": "http://125.74.131.242:9000/alg-prod/20250730/DD2025070300001 (2)_1753869046734.png",
        "modelVideoFileId": "1905",
        "modelVideoFileUrl": "http://125.74.131.242:9000/alg-prod/20250731/888f48310275899a449f79b9e5a41e78_1753897027966.mp4",
        "status": 0,
        "createTime": 1753893539000,
        "creator": "1",
        "updateTime": 1753893539000,
        "updater": "1",
        "deleted": false,
        "tenantId": 1
      },
      {
        "id": "1946760071329886213",
        "modelName": "张1",
        "modelFileId": "1899",
        "modelFileUrl": "http://125.74.131.242:9000/alg-prod/20250730/DD2025070300001 (2)_1753869046734.png",
        "modelVideoFileId": "1905",
        "modelVideoFileUrl": "http://125.74.131.242:9000/alg-prod/20250731/888f48310275899a449f79b9e5a41e78_1753897027966.mp4",
        "status": 2,
        "createTime": 1753893631000,
        "creator": "1",
        "updateTime": 1753894561000,
        "updater": "1",
        "deleted": false,
        "tenantId": 1
      },
      {
        "id": "1946760071329886214",
        "modelName": "张三",
        "modelFileId": "1899",
        "modelFileUrl": "http://125.74.131.242:9000/alg-prod/20250730/DD2025070300001 (2)_1753869046734.png",
        "modelVideoFileId": "1905",
        "modelVideoFileUrl": "http://125.74.131.242:9000/alg-prod/20250731/888f48310275899a449f79b9e5a41e78_1753897027966.mp4",
        "status": 0,
        "createTime": 1753894352000,
        "creator": "1",
        "updateTime": 1753894352000,
        "updater": "1",
        "deleted": false,
        "tenantId": 1
      },
      {
        "id": "1946760071329886215",
        "modelName": "张三",
        "modelFileId": "1899",
        "modelFileUrl": "http://125.74.131.242:9000/alg-prod/20250730/DD2025070300001 (2)_1753869046734.png",
        "modelVideoFileId": "1905",
        "modelVideoFileUrl": "http://125.74.131.242:9000/alg-prod/20250731/888f48310275899a449f79b9e5a41e78_1753897027966.mp4",
        "status": 0,
        "createTime": 1753894541000,
        "creator": "1",
        "updateTime": 1753894541000,
        "updater": "1",
        "deleted": false,
        "tenantId": 1
      },
      {
        "id": "1946760071329886216",
        "modelName": "张三",
        "modelFileId": "1899",
        "modelFileUrl": "http://125.74.131.242:9000/alg-prod/20250730/DD2025070300001 (2)_1753869046734.png",
        "modelVideoFileId": "1905",
        "modelVideoFileUrl": "http://125.74.131.242:9000/alg-prod/20250731/888f48310275899a449f79b9e5a41e78_1753897027966.mp4",
        "status": 0,
        "createTime": 1753894555000,
        "creator": "1",
        "updateTime": 1753894555000,
        "updater": "1",
        "deleted": false,
        "tenantId": 1
      }
    ],
    "total": 7
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultAiModelRespVO](#schemacommonresultpageresultaimodelrespvo)|

## GET 导出模型 Excel

GET /alg/ai-model/export-excel

导出模型 Excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |ID|
|modelName|query|string| 否 |模型名称|
|modelFileId|query|string| 否 |关联的模型文件ID|
|coverImageFileId|query|integer(int64)| 否 |模型封面图片文件id|
|modelVideoFileId|query|string| 否 |模型宣传视频文件id|
|status|query|integer| 否 |启用状态：0-停用，1-启用|
|createTime|query|array[string]| 否 |创建时间|
|creator|query|string| 否 |创建人|
|updateTime|query|array[string]| 否 |更新时间|
|updater|query|string| 否 |更新人|
|deleted|query|boolean| 否 |0代表存在 2代表删除|
|tenantId|query|integer(int64)| 否 |租户编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 管理后台 - 数据集

## POST 创建数据集

POST /alg/dataset/create

创建数据集

> Body 请求参数

```json
{
  "id": 11095,
  "name": "李四",
  "type": 0,
  "coverImageFileId": 14429,
  "status": 1,
  "markFlag": 0,
  "creator": "string",
  "updater": "string",
  "deleted": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[DatasetSaveReqVO](#schemadatasetsavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

<a id="opIdupdateDataset"></a>

## PUT 更新数据集

PUT /admin-api/alg/dataset/update

> Body 请求参数

```json
{
  "id": 11095,
  "name": "李四",
  "type": 0,
  "coverImageFileId": 14429,
  "status": 1,
  "markFlag": 0,
  "creator": "string",
  "updater": "string",
  "deleted": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[DatasetSaveReqVO](#schemadatasetsavereqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

## PUT 更新数据集

PUT /alg/dataset/update

更新数据集

> Body 请求参数

```json
{
  "id": 11095,
  "name": "李四",
  "type": 0,
  "coverImageFileId": 14429,
  "status": 1,
  "markFlag": 0,
  "creator": "string",
  "updater": "string",
  "deleted": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[DatasetSaveReqVO](#schemadatasetsavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 删除数据集

DELETE /alg/dataset/delete

删除数据集

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 批量删除数据集

DELETE /alg/dataset/delete-list

批量删除数据集

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获得数据集

GET /alg/dataset/get

获得数据集

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "name": "",
    "status": 0,
    "markFlag": 0,
    "createTime": "",
    "creator": "",
    "creatorName": "",
    "updater": "",
    "deleted": "",
    "dataSourceSize": 0,
    "dataSourceNum": 0,
    "datasetFiles": [
      {
        "id": 0,
        "datasetId": 0,
        "datasetIds": [
          0
        ],
        "fileName": "",
        "type": 0,
        "fileId": 0,
        "aiProcessed": 0,
        "createTime": "",
        "updateTime": "",
        "updater": "",
        "deleted": "",
        "createDept": 0,
        "creator": "",
        "fileUrl": "",
        "fileSize": 0
      }
    ],
    "coverImageFileId": 0,
    "coverImageFileUrl": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultDatasetRespVO](#schemacommonresultdatasetrespvo)|

<a id="opIdexportDatasetExcel"></a>

## GET 导出数据集 Excel

GET /admin-api/alg/dataset/export-excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|name|query|string| 否 |数据集名称|
|status|query|integer(int32)| 否 |上传状态|
|type|query|integer(int32)| 否 |数据集类型|
|markFlag|query|integer(int32)| 否 |是否标注 0 否；1是|
|createTime|query|array[string]| 否 |创建时间|
|creator|query|string| 否 |创建人|
|updater|query|string| 否 |更新人|
|deleted|query|string| 否 |0代表存在 2代表删除|
|pageNo|query|integer(int32)| 是 |页码，从 1 开始|
|pageSize|query|integer(int32)| 是 |每页条数，最大值为 100|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|

### 返回数据结构

## GET 获得数据集分页

GET /alg/dataset/page

获得数据集分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|name|query|string| 否 |数据集名称|
|status|query|integer| 否 |上传状态|
|type|query|integer| 否 |数据集类型|
|markFlag|query|integer| 否 |是否标注 0 否；1是|
|createTime|query|array[string]| 否 |创建时间|
|creator|query|string| 否 |创建人|
|updater|query|string| 否 |更新人|
|deleted|query|string| 否 |0代表存在 2代表删除|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "name": "",
        "status": 0,
        "markFlag": 0,
        "createTime": "",
        "creator": "",
        "updater": "",
        "deleted": ""
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultDatasetRespVO](#schemacommonresultpageresultdatasetrespvo)|

## GET 导出数据集 Excel

GET /alg/dataset/export-excel

导出数据集 Excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|name|query|string| 否 |数据集名称|
|status|query|integer| 否 |上传状态|
|type|query|integer| 否 |数据集类型|
|markFlag|query|integer| 否 |是否标注 0 否；1是|
|createTime|query|array[string]| 否 |创建时间|
|creator|query|string| 否 |创建人|
|updater|query|string| 否 |更新人|
|deleted|query|string| 否 |0代表存在 1代表删除|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 管理后台 - 流设备

## POST 创建视频流设备

POST /alg/resource-device/create

创建视频流设备

> Body 请求参数

```json
{
  "id": 28557,
  "deviceCode": "string",
  "deviceName": "张三",
  "ip": "string",
  "port": 0,
  "description": "你猜",
  "username": "李四",
  "password": "string",
  "channel": "string",
  "liveUrl": "https://www.iocoder.cn",
  "status": 1,
  "createTime": "string",
  "creator": 0,
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "deptId": 31191,
  "tenantId": 30909
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ResourceDeviceSaveReqVO](#schemaresourcedevicesavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## PUT 更新视频流设备

PUT /alg/resource-device/update

更新视频流设备

> Body 请求参数

```json
{
  "id": 28557,
  "deviceCode": "string",
  "deviceName": "张三",
  "ip": "string",
  "port": 0,
  "description": "你猜",
  "username": "李四",
  "password": "string",
  "channel": "string",
  "liveUrl": "https://www.iocoder.cn",
  "status": 1,
  "createTime": "string",
  "creator": 0,
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "deptId": 31191,
  "tenantId": 30909
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ResourceDeviceSaveReqVO](#schemaresourcedevicesavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 删除视频流设备

DELETE /alg/resource-device/delete

删除视频流设备

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 批量删除资源设备

DELETE /alg/resource-device/delete-list

批量删除资源设备

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获得视频流设备

GET /alg/resource-device/get

获得视频流设备

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "deviceCode": "",
    "deviceName": "",
    "ip": "",
    "port": 0,
    "description": "",
    "username": "",
    "password": "",
    "channel": "",
    "liveUrl": "",
    "status": 0,
    "createTime": "",
    "creator": 0,
    "updateTime": "",
    "updater": "",
    "deleted": false,
    "deptId": 0,
    "tenantId": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultResourceDeviceRespVO](#schemacommonresultresourcedevicerespvo)|

## GET 获得视频流设备分页

GET /alg/resource-device/page

获得视频流设备分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |主键ID|
|deviceCode|query|string| 否 |设备编号|
|deviceName|query|string| 否 |设备名称|
|ip|query|string| 否 |ip地址|
|port|query|integer| 否 |端口|
|description|query|string| 否 |描述|
|username|query|string| 否 |设备账号|
|password|query|string| 否 |密码|
|channel|query|string| 否 |视频流通道|
|liveUrl|query|string| 否 |视频链接|
|status|query|integer| 否 |0-离线，10-在线|
|createTime|query|array[string]| 否 |创建时间|
|creator|query|integer(int64)| 否 |创建人|
|updateTime|query|array[string]| 否 |更新时间|
|updater|query|string| 否 |更新人|
|deleted|query|boolean| 否 |删除标志（0代表存在 2代表删除）|
|deptId|query|integer(int64)| 否 |所属部门|
|tenantId|query|integer(int64)| 否 |租户编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "deviceCode": "",
        "deviceName": "",
        "ip": "",
        "port": 0,
        "description": "",
        "username": "",
        "password": "",
        "channel": "",
        "liveUrl": "",
        "status": 0,
        "createTime": "",
        "creator": 0,
        "updateTime": "",
        "updater": "",
        "deleted": false,
        "deptId": 0,
        "tenantId": 0
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultResourceDeviceRespVO](#schemacommonresultpageresultresourcedevicerespvo)|

## GET 导出视频流设备 Excel

GET /alg/resource-device/export-excel

导出视频流设备 Excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |主键ID|
|deviceCode|query|string| 否 |设备编号|
|deviceName|query|string| 否 |设备名称|
|ip|query|string| 否 |ip地址|
|port|query|integer| 否 |端口|
|description|query|string| 否 |描述|
|username|query|string| 否 |设备账号|
|password|query|string| 否 |密码|
|channel|query|string| 否 |视频流通道|
|liveUrl|query|string| 否 |视频链接|
|status|query|integer| 否 |0-离线，10-在线|
|createTime|query|array[string]| 否 |创建时间|
|creator|query|integer(int64)| 否 |创建人|
|updateTime|query|array[string]| 否 |更新时间|
|updater|query|string| 否 |更新人|
|deleted|query|boolean| 否 |删除标志（0代表存在 2代表删除）|
|deptId|query|integer(int64)| 否 |所属部门|
|tenantId|query|integer(int64)| 否 |租户编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 管理后台 - 任务

## POST 创建任务

POST /alg/task/create

创建任务

> Body 请求参数

```json
{
  "id": 27454,
  "name": "任务001",
  "taskType": 0,
  "taskDataId": 29021,
  "taskDataName": 29021,
  "status": 1,
  "aiModelId": "5308",
  "aiModelName": "模型名称",
  "alarmNoticeUserId": "2717",
  "reason": "不好",
  "creator": "string",
  "createTime": "string",
  "updater": "string",
  "deleted": true,
  "updateTime": "string",
  "tenantId": 9293
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[TaskSaveReqVO](#schematasksavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## PUT 更新任务

PUT /alg/task/update

更新任务

> Body 请求参数

```json
{
  "id": 27454,
  "name": "任务001",
  "taskType": 0,
  "taskDataId": 29021,
  "taskDataName": 29021,
  "status": 1,
  "aiModelId": "5308",
  "aiModelName": "模型名称",
  "alarmNoticeUserId": "2717",
  "reason": "不好",
  "creator": "string",
  "createTime": "string",
  "updater": "string",
  "deleted": true,
  "updateTime": "string",
  "tenantId": 9293
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[TaskSaveReqVO](#schematasksavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 删除任务

DELETE /alg/task/delete

删除任务

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 批量删除任务

DELETE /alg/task/delete-list

批量删除任务

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获得任务

GET /alg/task/get

获得任务

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "name": "",
    "taskType": 0,
    "taskDataId": 0,
    "status": 0,
    "aiModelId": "",
    "aiModelName": "",
    "alarmNoticeUserId": "",
    "reason": "",
    "creator": "",
    "createTime": "",
    "updater": "",
    "deleted": false,
    "updateTime": "",
    "tenantId": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultTaskRespVO](#schemacommonresulttaskrespvo)|

## GET 获得任务分页

GET /alg/task/page

获得任务分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |ID|
|name|query|string| 否 |识别报告名称|
|taskType|query|integer| 否 |任务类型 0数据集；1视频流|
|taskDataId|query|integer(int64)| 否 |数据集或者视频流的id，类型根据task_type判断|
|status|query|integer| 否 |识别状态|
|aiModelId|query|string| 否 |识别模型id|
|aiModelName|query|string| 否 |识别模型名称|
|alarmNoticeUserId|query|string| 否 |告警通知人员id，多个用逗号隔开|
|reason|query|string| 否 |识别失败原因|
|creator|query|string| 否 |创建人|
|createTime|query|array[string]| 否 |创建时间|
|updater|query|string| 否 |更新人|
|deleted|query|boolean| 否 |0代表存在 2代表删除|
|updateTime|query|array[string]| 否 |更新时间|
|tenantId|query|integer(int64)| 否 |租户编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "name": "",
        "taskType": 0,
        "taskDataId": 0,
        "status": 0,
        "aiModelId": "",
        "aiModelName": "",
        "alarmNoticeUserId": "",
        "reason": "",
        "creator": "",
        "createTime": "",
        "updater": "",
        "deleted": false,
        "updateTime": "",
        "tenantId": 0
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultTaskRespVO](#schemacommonresultpageresulttaskrespvo)|

## GET 导出任务 Excel

GET /alg/task/export-excel

导出任务 Excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |ID|
|name|query|string| 否 |识别报告名称|
|taskType|query|integer| 否 |任务类型 0数据集；1视频流|
|taskDataId|query|integer(int64)| 否 |数据集或者视频流的id，类型根据task_type判断|
|status|query|integer| 否 |识别状态|
|aiModelId|query|string| 否 |识别模型id|
|aiModelName|query|string| 否 |识别模型名称|
|alarmNoticeUserId|query|string| 否 |告警通知人员id，多个用逗号隔开|
|reason|query|string| 否 |识别失败原因|
|creator|query|string| 否 |创建人|
|createTime|query|array[string]| 否 |创建时间|
|updater|query|string| 否 |更新人|
|deleted|query|boolean| 否 |0代表存在 2代表删除|
|updateTime|query|array[string]| 否 |更新时间|
|tenantId|query|integer(int64)| 否 |租户编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 执行任务

GET /alg/task/start-task

执行任务

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |id|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

# 管理后台 - 任务资源文件

## POST 创建任务资源文件

POST /alg/task-files/create

创建任务资源文件

> Body 请求参数

```json
{
  "id": 32320,
  "taskId": 2269,
  "taskFileId": 9678,
  "type": 2,
  "originalFileUrl": "https://www.iocoder.cn",
  "processedFileUrl": "https://www.iocoder.cn",
  "processedFileId": "24399",
  "hasAlarm": 0,
  "status": 1,
  "creator": "string",
  "resultJsonFileId": "12959",
  "updater": "string",
  "resultJsonFileUrl": "https://www.iocoder.cn",
  "deleted": true,
  "createTime": "string",
  "tenantId": 26007,
  "updateTime": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[TaskFilesSaveReqVO](#schemataskfilessavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## PUT 更新任务资源文件

PUT /alg/task-files/update

更新任务资源文件

> Body 请求参数

```json
{
  "id": 32320,
  "taskId": 2269,
  "taskFileId": 9678,
  "type": 2,
  "originalFileUrl": "https://www.iocoder.cn",
  "processedFileUrl": "https://www.iocoder.cn",
  "processedFileId": "24399",
  "hasAlarm": 0,
  "status": 1,
  "creator": "string",
  "resultJsonFileId": "12959",
  "updater": "string",
  "resultJsonFileUrl": "https://www.iocoder.cn",
  "deleted": true,
  "createTime": "string",
  "tenantId": 26007,
  "updateTime": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[TaskFilesSaveReqVO](#schemataskfilessavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 删除任务资源文件

DELETE /alg/task-files/delete

删除任务资源文件

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 批量删除任务资源文件

DELETE /alg/task-files/delete-list

批量删除任务资源文件

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获得任务文件：视频、图片

GET /alg/task-files/get

获得任务文件：视频、图片

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "taskId": 0,
    "taskFileId": 0,
    "type": 0,
    "originalFileUrl": "",
    "processedFileUrl": "",
    "processedFileId": "",
    "hasAlarm": 0,
    "status": 0,
    "creator": "",
    "resultJsonFileId": "",
    "updater": "",
    "resultJsonFileUrl": "",
    "deleted": false,
    "createTime": "",
    "tenantId": 0,
    "updateTime": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultTaskFilesRespVO](#schemacommonresulttaskfilesrespvo)|

## GET 获得任务任务资源文件分页

GET /alg/task-files/page

获得任务任务资源文件分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |ID|
|taskId|query|integer(int64)| 否 |识别任务id|
|taskFileId|query|integer(int64)| 否 |关联的识别文件id|
|type|query|integer| 否 |文件类型：0-图片，1-视频|
|originalFileUrl|query|string| 否 |被识别的文件url|
|processedFileUrl|query|string| 否 |识别后的文件url|
|processedFileId|query|string| 否 |识别后的文件id|
|hasAlarm|query|integer| 否 |是否有隐患：0-否，1-是|
|status|query|integer| 否 |识别状态：0-识别中，10-识别完成，20-识别失败|
|creator|query|string| 否 |创建人|
|resultJsonFileId|query|string| 否 |识别结果json文件id|
|updater|query|string| 否 |更新人|
|resultJsonFileUrl|query|string| 否 |识别结果json文件url|
|deleted|query|boolean| 否 |0代表存在 2代表删除|
|createTime|query|array[string]| 否 |创建时间|
|tenantId|query|integer(int64)| 否 |租户编号|
|updateTime|query|array[string]| 否 |更新时间|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "taskId": 0,
        "taskFileId": 0,
        "type": 0,
        "originalFileUrl": "",
        "processedFileUrl": "",
        "processedFileId": "",
        "hasAlarm": 0,
        "status": 0,
        "creator": "",
        "resultJsonFileId": "",
        "updater": "",
        "resultJsonFileUrl": "",
        "deleted": false,
        "createTime": "",
        "tenantId": 0,
        "updateTime": ""
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultTaskFilesRespVO](#schemacommonresultpageresulttaskfilesrespvo)|

## GET 导出任务资源文件 Excel

GET /alg/task-files/export-excel

导出任务资源文件 Excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |ID|
|taskId|query|integer(int64)| 否 |识别任务id|
|taskFileId|query|integer(int64)| 否 |关联的识别文件id|
|type|query|integer| 否 |文件类型：0-图片，1-视频|
|originalFileUrl|query|string| 否 |被识别的文件url|
|processedFileUrl|query|string| 否 |识别后的文件url|
|processedFileId|query|string| 否 |识别后的文件id|
|hasAlarm|query|integer| 否 |是否有隐患：0-否，1-是|
|status|query|integer| 否 |识别状态：0-识别中，10-识别完成，20-识别失败|
|creator|query|string| 否 |创建人|
|resultJsonFileId|query|string| 否 |识别结果json文件id|
|updater|query|string| 否 |更新人|
|resultJsonFileUrl|query|string| 否 |识别结果json文件url|
|deleted|query|boolean| 否 |0代表存在 2代表删除|
|createTime|query|array[string]| 否 |创建时间|
|tenantId|query|integer(int64)| 否 |租户编号|
|updateTime|query|array[string]| 否 |更新时间|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 管理后台 - 告警明细

## POST 创建告警明细

POST /alg/alarm-details/create

创建告警明细

> Body 请求参数

```json
{
  "id": 17200,
  "taskId": 22076,
  "taskFileId": 27440,
  "alarmType": "2",
  "alarmTypeName": "张三",
  "confidence": "string",
  "alarmFileUrl": "https://www.iocoder.cn",
  "alarmFileId": "15350",
  "alarmTime": "string",
  "createTime": "string",
  "creator": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "tenantId": 16799
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlarmDetailsSaveReqVO](#schemaalarmdetailssavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## PUT 更新告警明细

PUT /alg/alarm-details/update

更新告警明细

> Body 请求参数

```json
{
  "id": 17200,
  "taskId": 22076,
  "taskFileId": 27440,
  "alarmType": "2",
  "alarmTypeName": "张三",
  "confidence": "string",
  "alarmFileUrl": "https://www.iocoder.cn",
  "alarmFileId": "15350",
  "alarmTime": "string",
  "createTime": "string",
  "creator": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "tenantId": 16799
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlarmDetailsSaveReqVO](#schemaalarmdetailssavereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 删除告警明细

DELETE /alg/alarm-details/delete

删除告警明细

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 批量删除告警明细

DELETE /alg/alarm-details/delete-list

批量删除告警明细

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获得告警明细

GET /alg/alarm-details/get

获得告警明细

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "taskId": 0,
    "taskFileId": 0,
    "alarmType": "",
    "alarmTypeName": "",
    "confidence": "",
    "alarmFileUrl": "",
    "alarmFileId": "",
    "alarmTime": "",
    "createTime": "",
    "creator": "",
    "updateTime": "",
    "updater": "",
    "deleted": false,
    "tenantId": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAlarmDetailsRespVO](#schemacommonresultalarmdetailsrespvo)|

## GET 获得告警信息-明细分页

GET /alg/alarm-details/page

获得告警信息-明细分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |ID|
|taskId|query|integer(int64)| 否 |识别任务id|
|taskFileId|query|integer(int64)| 否 |关联的任务文件id|
|alarmType|query|string| 否 |隐患类型|
|alarmTypeName|query|string| 否 |隐患类型名称|
|confidence|query|string| 否 |置信度|
|alarmFileUrl|query|string| 否 |结果文件url，视频隐患截图|
|alarmFileId|query|string| 否 |结果文件id，视频隐患截图|
|alarmTime|query|array[string]| 否 |发生隐患的视频第几秒|
|createTime|query|array[string]| 否 |创建时间|
|creator|query|string| 否 |创建人|
|updateTime|query|array[string]| 否 |更新时间|
|updater|query|string| 否 |更新人|
|deleted|query|boolean| 否 |0代表存在 2代表删除|
|tenantId|query|integer(int64)| 否 |租户编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "taskId": 0,
        "taskFileId": 0,
        "alarmType": "",
        "alarmTypeName": "",
        "confidence": "",
        "alarmFileUrl": "",
        "alarmFileId": "",
        "alarmTime": "",
        "createTime": "",
        "creator": "",
        "updateTime": "",
        "updater": "",
        "deleted": false,
        "tenantId": 0
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultAlarmDetailsRespVO](#schemacommonresultpageresultalarmdetailsrespvo)|

## GET 导出告警明细 Excel

GET /alg/alarm-details/export-excel

导出告警明细 Excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|id|query|integer(int64)| 否 |ID|
|taskId|query|integer(int64)| 否 |识别任务id|
|taskFileId|query|integer(int64)| 否 |关联的任务文件id|
|alarmType|query|string| 否 |隐患类型|
|alarmTypeName|query|string| 否 |隐患类型名称|
|confidence|query|string| 否 |置信度|
|alarmFileUrl|query|string| 否 |结果文件url，视频隐患截图|
|alarmFileId|query|string| 否 |结果文件id，视频隐患截图|
|alarmTime|query|array[string]| 否 |发生隐患的视频第几秒|
|createTime|query|array[string]| 否 |创建时间|
|creator|query|string| 否 |创建人|
|updateTime|query|array[string]| 否 |更新时间|
|updater|query|string| 否 |更新人|
|deleted|query|boolean| 否 |0代表存在 2代表删除|
|tenantId|query|integer(int64)| 否 |租户编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 告警通知

POST /alg/alarm-details/notify

算法过来的告警通知信息
告警通知

> Body 请求参数

```json
{
  "taskUniqueId": "27440",
  "fileUniqueId": "27440",
  "jsonFileUrl": "27440",
  "alarmImgFileUrl": "27440"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[AlarmNotifyDTO](#schemaalarmnotifydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {},
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResult](#schemacommonresult)|

# 管理后台 - 字典数据

<a id="opIdupdateDictData"></a>

## PUT 修改字典数据

PUT /admin-api/system/dict-data/update

> Body 请求参数

```json
{
  "id": 1024,
  "sort": 1024,
  "label": "芋道",
  "value": "iocoder",
  "dictType": "sys_common_sex",
  "status": 1,
  "colorType": "default",
  "cssClass": "btn-visible",
  "remark": "我是一个角色"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[DictDataSaveReqVO](#schemadictdatasavereqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

<a id="opIdcreateDictData"></a>

## POST 新增字典数据

POST /admin-api/system/dict-data/create

> Body 请求参数

```json
{
  "id": 1024,
  "sort": 1024,
  "label": "芋道",
  "value": "iocoder",
  "dictType": "sys_common_sex",
  "status": 1,
  "colorType": "default",
  "cssClass": "btn-visible",
  "remark": "我是一个角色"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[DictDataSaveReqVO](#schemadictdatasavereqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultLong](#schemacommonresultlong)|

<a id="opIdgetDictTypePage"></a>

## GET 获得字典类型的分页

GET /admin-api/system/dict-data/page

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|label|query|string| 否 |字典标签|
|dictType|query|string| 否 |字典类型，模糊匹配|
|status|query|integer(int32)| 否 |展示状态，参见 CommonStatusEnum 枚举类|
|pageNo|query|integer(int32)| 是 |页码，从 1 开始|
|pageSize|query|integer(int32)| 是 |每页条数，最大值为 100|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":{"list":[{"id":1024,"sort":1024,"label":"芋道","value":"iocoder","dictType":"sys_common_sex","status":1,"colorType":"default","cssClass":"btn-visible","remark":"我是一个角色","createTime":"时间戳格式"}],"total":0},"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultPageResultDictDataRespVO](#schemacommonresultpageresultdictdatarespvo)|

<a id="opIdgetSimpleDictDataList"></a>

## GET 获得全部字典数据列表

GET /admin-api/system/dict-data/list-all-simple

一般用于管理后台缓存字典数据在本地

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":[{"dictType":"gender","value":1,"label":"男","colorType":"default","cssClass":"btn-visible"}],"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultListDictDataSimpleRespVO](#schemacommonresultlistdictdatasimplerespvo)|

<a id="opIdgetSimpleDictDataList_1"></a>

## GET 获得全部字典数据列表

GET /admin-api/system/dict-data/simple-list

一般用于管理后台缓存字典数据在本地

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":[{"dictType":"gender","value":1,"label":"男","colorType":"default","cssClass":"btn-visible"}],"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultListDictDataSimpleRespVO](#schemacommonresultlistdictdatasimplerespvo)|

<a id="opIdgetDictData"></a>

## GET /查询字典数据详细

GET /admin-api/system/dict-data/get

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":{"id":1024,"sort":1024,"label":"芋道","value":"iocoder","dictType":"sys_common_sex","status":1,"colorType":"default","cssClass":"btn-visible","remark":"我是一个角色","createTime":"时间戳格式"},"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultDictDataRespVO](#schemacommonresultdictdatarespvo)|

<a id="opIdexport_3"></a>

## GET 导出字典数据

GET /admin-api/system/dict-data/export-excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|label|query|string| 否 |字典标签|
|dictType|query|string| 否 |字典类型，模糊匹配|
|status|query|integer(int32)| 否 |展示状态，参见 CommonStatusEnum 枚举类|
|pageNo|query|integer(int32)| 是 |页码，从 1 开始|
|pageSize|query|integer(int32)| 是 |每页条数，最大值为 100|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|Inline|

### 返回数据结构

<a id="opIddeleteDictData"></a>

## DELETE 删除字典数据

DELETE /admin-api/system/dict-data/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

<a id="opIddeleteDictDataList"></a>

## DELETE 批量删除字典数据

DELETE /admin-api/system/dict-data/delete-list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号列表|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

# 管理后台 - 文件存储

## POST 上传文件 一般文件

POST /infra/file/upload

上传文件
模式一：后端上传文件

> Body 请求参数

```yaml
file: string
directory: XXX/YYY

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |文件附件|
|» directory|body|string| 否 |文件目录|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": "",
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultString](#schemacommonresultstring)|

## GET 获取文件预签名地址

GET /infra/file/presigned-url

获取文件预签名地址
模式二：前端上传文件：用于前端直接上传七牛、阿里云 OSS 等文件存储器

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|name|query|string| 是 |文件名称|
|directory|query|string| 否 |文件目录|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "configId": 0,
    "uploadUrl": "",
    "url": "",
    "path": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultFilePresignedUrlRespVO](#schemacommonresultfilepresignedurlrespvo)|

## POST 创建文件

POST /infra/file/create

创建文件
模式二：前端上传文件：配合 presigned-url 接口，记录上传了上传的文件

> Body 请求参数

```json
{
  "configId": 11,
  "path": "yudao.jpg",
  "name": "yudao.jpg",
  "url": "https://www.iocoder.cn/yudao.jpg",
  "type": "application/octet-stream",
  "size": 2048
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[FileCreateReqVO](#schemafilecreatereqvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## DELETE 删除文件

DELETE /infra/file/delete

删除文件

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 批量删除文件

DELETE /infra/file/delete-list

批量删除文件

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号列表|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 下载文件

GET /infra/file/{configId}/get/**

下载文件

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|configId|path|integer| 是 |none|
|configId|query|any| 是 |配置编号|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 获得文件分页

GET /infra/file/page

获得文件分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|path|query|string| 否 |文件路径，模糊匹配|
|type|query|string| 否 |文件类型，模糊匹配|
|createTime|query|array[string]| 否 |创建时间|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "configId": 0,
        "path": "",
        "name": "",
        "url": "",
        "type": "",
        "size": 0,
        "createTime": ""
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultFileRespVO](#schemacommonresultpageresultfilerespvo)|

## GET 分片上传-1 初始化分片上传

GET /infra/file/init-multipart-upload

初始化分片上传

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|objectKey|query|string| 否 |对象键(文件在存储桶中的路径)|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

## POST 分片上传-2 上传分片

POST /infra/file/upload-part

上传分片
分片上传-2 上传分片

> Body 请求参数

```yaml
uploadId: "1"
partNumber: 1
multipartFile: jpg

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» uploadId|body|string| 否 |上传ID|
|» partNumber|body|integer| 否 |分片编号(从1开始)|
|» multipartFile|body|string(binary)| 否 |本地文件|

> 返回示例

> 200 Response

```json
{
  "eTag": "",
  "checksumCRC32": "",
  "checksumCRC32C": "",
  "checksumCRC64NVME": "",
  "checksumSHA1": "",
  "checksumSHA256": "",
  "partNumber": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RedisUploadPartDTO](#schemaredisuploadpartdto)|

## POST 分片上传-3 合并分片

POST /infra/file/complete-upload-part

完成分片上传(合并分片)
分片上传-3 合并分片

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|uploadId|query|string| 否 |上传ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

## GET 分片上传 取消分片上传

GET /infra/file/abort-upload-part

取消分片上传
分片上传 取消分片上传

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|objectKey|query|string| 否 |对象键|
|uploadId|query|string| 否 |上传ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 分片上传 获取已经上传的分片列表

GET /infra/file/part-list

获取已上传的分片列表
分片上传 获取已经上传的分片列表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|objectKey|query|string| 否 |对象键|
|uploadId|query|string| 否 |上传ID|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
[
  {
    "partNumber": 0,
    "lastModified": {
      "seconds": 0,
      "nanos": 0
    },
    "eTag": "",
    "size": 0,
    "checksumCRC32": "",
    "checksumCRC32C": "",
    "checksumCRC64NVME": "",
    "checksumSHA1": "",
    "checksumSHA256": ""
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*已上传的分片列表*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[Part](#schemapart)]|false|none||已上传的分片列表|
|» partNumber|integer|false|none||none|
|» lastModified|[Instant](#schemainstant)|false|none||none|
|»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|» eTag|string|false|none||none|
|» size|integer(int64)|false|none||none|
|» checksumCRC32|string|false|none||none|
|» checksumCRC32C|string|false|none||none|
|» checksumCRC64NVME|string|false|none||none|
|» checksumSHA1|string|false|none||none|
|» checksumSHA256|string|false|none||none|

## GET 分片上传 执行完成分片上传-测试

GET /infra/file/part-upload-file-test

执行完整的分片上传流程
分片上传 执行完成分片上传-测试

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|localFilePath|query|string| 否 |本地文件路径|
|objectKey|query|string| 否 |存储到S3的对象键|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
"string"
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|string|

# 数据模型

<h2 id="tocS_UserSaveReqVO">UserSaveReqVO</h2>

<a id="schemausersavereqvo"></a>
<a id="schema_UserSaveReqVO"></a>
<a id="tocSusersavereqvo"></a>
<a id="tocsusersavereqvo"></a>

```json
{
  "id": 1024,
  "username": "yudao",
  "nickname": "芋艿",
  "remark": "我是一个用户",
  "deptId": "我是一个用户",
  "postIds": 1,
  "email": "<EMAIL>",
  "mobile": ***********,
  "sex": 1,
  "avatar": "https://www.iocoder.cn/xxx.png",
  "password": 123456
}

```

管理后台 - 用户创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||用户编号|
|username|string|true|none||用户账号|
|nickname|string|true|none||用户昵称|
|remark|string|false|none||备注|
|deptId|integer(int64)|false|none||部门编号|
|postIds|[integer]|false|none||岗位编号数组|
|email|string|false|none||用户邮箱|
|mobile|string|false|none||手机号码|
|sex|integer(int32)|false|none||用户性别，参见 SexEnum 枚举类|
|avatar|string|false|none||用户头像|
|password|string|true|none||密码|

<h2 id="tocS_UserUpdateStatusReqVO">UserUpdateStatusReqVO</h2>

<a id="schemauserupdatestatusreqvo"></a>
<a id="schema_UserUpdateStatusReqVO"></a>
<a id="tocSuserupdatestatusreqvo"></a>
<a id="tocsuserupdatestatusreqvo"></a>

```json
{
  "id": 1024,
  "status": 1
}

```

管理后台 - 用户更新状态 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||用户编号|
|status|integer(int32)|true|none||状态，见 CommonStatusEnum 枚举|

<h2 id="tocS_UserUpdatePasswordReqVO">UserUpdatePasswordReqVO</h2>

<a id="schemauserupdatepasswordreqvo"></a>
<a id="schema_UserUpdatePasswordReqVO"></a>
<a id="tocSuserupdatepasswordreqvo"></a>
<a id="tocsuserupdatepasswordreqvo"></a>

```json
{
  "id": 1024,
  "password": 123456
}

```

管理后台 - 用户更新密码 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||用户编号|
|password|string|true|none||密码|

<h2 id="tocS_UserProfileUpdateReqVO">UserProfileUpdateReqVO</h2>

<a id="schemauserprofileupdatereqvo"></a>
<a id="schema_UserProfileUpdateReqVO"></a>
<a id="tocSuserprofileupdatereqvo"></a>
<a id="tocsuserprofileupdatereqvo"></a>

```json
{
  "nickname": "芋艿",
  "email": "<EMAIL>",
  "mobile": ***********,
  "sex": 1,
  "avatar": "https://www.iocoder.cn/1.png"
}

```

管理后台 - 用户个人信息更新 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|nickname|string|false|none||用户昵称|
|email|string|false|none||用户邮箱|
|mobile|string|false|none||手机号码|
|sex|integer(int32)|false|none||用户性别，参见 SexEnum 枚举类|
|avatar|string|false|none||角色头像|

<h2 id="tocS_UserProfileUpdatePasswordReqVO">UserProfileUpdatePasswordReqVO</h2>

<a id="schemauserprofileupdatepasswordreqvo"></a>
<a id="schema_UserProfileUpdatePasswordReqVO"></a>
<a id="tocSuserprofileupdatepasswordreqvo"></a>
<a id="tocsuserprofileupdatepasswordreqvo"></a>

```json
{
  "oldPassword": 123456,
  "newPassword": 654321
}

```

管理后台 - 用户个人中心更新密码 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|oldPassword|string|true|none||旧密码|
|newPassword|string|true|none||新密码|

<h2 id="tocS_TenantSaveReqVO">TenantSaveReqVO</h2>

<a id="schematenantsavereqvo"></a>
<a id="schema_TenantSaveReqVO"></a>
<a id="tocStenantsavereqvo"></a>
<a id="tocstenantsavereqvo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "contactName": "芋艿",
  "contactMobile": ***********,
  "status": 1,
  "website": "https://www.iocoder.cn",
  "packageId": 1024,
  "expireTime": "2019-08-24T14:15:22Z",
  "accountCount": 1024,
  "username": "yudao",
  "password": 123456
}

```

管理后台 - 租户创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||租户编号|
|name|string|true|none||租户名|
|contactName|string|true|none||联系人|
|contactMobile|string|false|none||联系手机|
|status|integer(int32)|true|none||租户状态|
|website|string|false|none||绑定域名|
|packageId|integer(int64)|true|none||租户套餐编号|
|expireTime|string(date-time)|true|none||过期时间|
|accountCount|integer(int32)|true|none||账号数量|
|username|string|true|none||用户账号|
|password|string|true|none||密码|

<h2 id="tocS_TenantPackageSaveReqVO">TenantPackageSaveReqVO</h2>

<a id="schematenantpackagesavereqvo"></a>
<a id="schema_TenantPackageSaveReqVO"></a>
<a id="tocStenantpackagesavereqvo"></a>
<a id="tocstenantpackagesavereqvo"></a>

```json
{
  "id": 1024,
  "name": "VIP",
  "status": 1,
  "remark": "好",
  "menuIds": [
    0
  ]
}

```

管理后台 - 租户套餐创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||套餐编号|
|name|string|true|none||套餐名|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举|
|remark|string|false|none||备注|
|menuIds|[integer]|true|none||关联的菜单编号|

<h2 id="tocS_SocialClientSaveReqVO">SocialClientSaveReqVO</h2>

<a id="schemasocialclientsavereqvo"></a>
<a id="schema_SocialClientSaveReqVO"></a>
<a id="tocSsocialclientsavereqvo"></a>
<a id="tocssocialclientsavereqvo"></a>

```json
{
  "id": 27162,
  "name": "yudao商城",
  "socialType": 31,
  "userType": 2,
  "clientId": "wwd411c69a39ad2e54",
  "clientSecret": "peter",
  "agentId": 2000045,
  "status": 1
}

```

管理后台 - 社交客户端创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||编号|
|name|string|true|none||应用名|
|socialType|integer(int32)|true|none||社交平台的类型|
|userType|integer(int32)|true|none||用户类型|
|clientId|string|true|none||客户端编号|
|clientSecret|string|true|none||客户端密钥|
|agentId|string|true|none||授权方的网页应用编号|
|status|integer(int32)|true|none||状态|

<h2 id="tocS_SmsTemplateSaveReqVO">SmsTemplateSaveReqVO</h2>

<a id="schemasmstemplatesavereqvo"></a>
<a id="schema_SmsTemplateSaveReqVO"></a>
<a id="tocSsmstemplatesavereqvo"></a>
<a id="tocssmstemplatesavereqvo"></a>

```json
{
  "id": 1024,
  "type": 1,
  "status": 1,
  "code": "test_01",
  "name": "yudao",
  "content": "你好，{name}。你长的太{like}啦！",
  "remark": "哈哈哈",
  "apiTemplateId": 4383920,
  "channelId": 10
}

```

管理后台 - 短信模板创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||编号|
|type|integer(int32)|true|none||短信类型，参见 SmsTemplateTypeEnum 枚举类|
|status|integer(int32)|true|none||开启状态，参见 CommonStatusEnum 枚举类|
|code|string|true|none||模板编码|
|name|string|true|none||模板名称|
|content|string|true|none||模板内容|
|remark|string|false|none||备注|
|apiTemplateId|string|true|none||短信 API 的模板编号|
|channelId|integer(int64)|true|none||短信渠道编号|

<h2 id="tocS_SmsChannelSaveReqVO">SmsChannelSaveReqVO</h2>

<a id="schemasmschannelsavereqvo"></a>
<a id="schema_SmsChannelSaveReqVO"></a>
<a id="tocSsmschannelsavereqvo"></a>
<a id="tocssmschannelsavereqvo"></a>

```json
{
  "id": 1024,
  "signature": "芋道源码",
  "code": "YUN_PIAN",
  "status": 1,
  "remark": "好吃！",
  "apiKey": "yudao",
  "apiSecret": "yuanma",
  "callbackUrl": "http://www.iocoder.cn"
}

```

管理后台 - 短信渠道创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||编号|
|signature|string|true|none||短信签名|
|code|string|true|none||渠道编码，参见 SmsChannelEnum 枚举类|
|status|integer(int32)|true|none||启用状态|
|remark|string|false|none||备注|
|apiKey|string|true|none||短信 API 的账号|
|apiSecret|string|false|none||短信 API 的密钥|
|callbackUrl|string|false|none||短信发送回调 URL|

<h2 id="tocS_RoleSaveReqVO">RoleSaveReqVO</h2>

<a id="schemarolesavereqvo"></a>
<a id="schema_RoleSaveReqVO"></a>
<a id="tocSrolesavereqvo"></a>
<a id="tocsrolesavereqvo"></a>

```json
{
  "id": 1,
  "name": "管理员",
  "code": "ADMIN",
  "sort": 1024,
  "status": 0,
  "remark": "我是一个角色"
}

```

管理后台 - 角色创建/更新 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||角色编号|
|name|string|true|none||角色名称|
|code|string|true|none||角色标志|
|sort|integer(int32)|true|none||显示顺序|
|status|integer(int32)|true|none||状态|
|remark|string|false|none||备注|

<h2 id="tocS_PostSaveReqVO">PostSaveReqVO</h2>

<a id="schemapostsavereqvo"></a>
<a id="schema_PostSaveReqVO"></a>
<a id="tocSpostsavereqvo"></a>
<a id="tocspostsavereqvo"></a>

```json
{
  "id": 1024,
  "name": "小土豆",
  "code": "yudao",
  "sort": 1024,
  "status": 1,
  "remark": "快乐的备注"
}

```

管理后台 - 岗位创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||岗位编号|
|name|string|true|none||岗位名称|
|code|string|true|none||岗位编码|
|sort|integer(int32)|true|none||显示顺序|
|status|integer(int32)|true|none||状态|
|remark|string|false|none||备注|

<h2 id="tocS_OAuth2UserUpdateReqVO">OAuth2UserUpdateReqVO</h2>

<a id="schemaoauth2userupdatereqvo"></a>
<a id="schema_OAuth2UserUpdateReqVO"></a>
<a id="tocSoauth2userupdatereqvo"></a>
<a id="tocsoauth2userupdatereqvo"></a>

```json
{
  "nickname": "芋艿",
  "email": "<EMAIL>",
  "mobile": ***********,
  "sex": 1
}

```

管理后台 - OAuth2 更新用户基本信息 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|nickname|string|true|none||用户昵称|
|email|string|false|none||用户邮箱|
|mobile|string|false|none||手机号码|
|sex|integer(int32)|false|none||用户性别，参见 SexEnum 枚举类|

<h2 id="tocS_OAuth2ClientSaveReqVO">OAuth2ClientSaveReqVO</h2>

<a id="schemaoauth2clientsavereqvo"></a>
<a id="schema_OAuth2ClientSaveReqVO"></a>
<a id="tocSoauth2clientsavereqvo"></a>
<a id="tocsoauth2clientsavereqvo"></a>

```json
{
  "id": 1024,
  "clientId": "tudou",
  "secret": "fan",
  "name": "土豆",
  "logo": "https://www.iocoder.cn/xx.png",
  "description": "我是一个应用",
  "status": 1,
  "accessTokenValiditySeconds": 8640,
  "refreshTokenValiditySeconds": 8640000,
  "redirectUris": "https://www.iocoder.cn",
  "authorizedGrantTypes": "password",
  "scopes": "user_info",
  "autoApproveScopes": "user_info",
  "authorities": "system:user:query",
  "resourceIds": 1024,
  "additionalInformation": "{yunai: true}",
  "additionalInformationJson": true
}

```

管理后台 - OAuth2 客户端创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||编号|
|clientId|string|true|none||客户端编号|
|secret|string|true|none||客户端密钥|
|name|string|true|none||应用名|
|logo|string|true|none||应用图标|
|description|string|false|none||应用描述|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举|
|accessTokenValiditySeconds|integer(int32)|true|none||访问令牌的有效期|
|refreshTokenValiditySeconds|integer(int32)|true|none||刷新令牌的有效期|
|redirectUris|[string]|true|none||可重定向的 URI 地址|
|authorizedGrantTypes|[string]|true|none||授权类型，参见 OAuth2GrantTypeEnum 枚举|
|scopes|[string]|false|none||授权范围|
|autoApproveScopes|[string]|false|none||自动通过的授权范围|
|authorities|[string]|false|none||权限|
|resourceIds|[string]|false|none||资源|
|additionalInformation|string|false|none||附加信息|
|additionalInformationJson|boolean|false|none||none|

<h2 id="tocS_NotifyTemplateSaveReqVO">NotifyTemplateSaveReqVO</h2>

<a id="schemanotifytemplatesavereqvo"></a>
<a id="schema_NotifyTemplateSaveReqVO"></a>
<a id="tocSnotifytemplatesavereqvo"></a>
<a id="tocsnotifytemplatesavereqvo"></a>

```json
{
  "id": 1024,
  "name": "测试模版",
  "code": "SEND_TEST",
  "type": 1,
  "nickname": "土豆",
  "content": "我是模版内容",
  "status": 1,
  "remark": "我是备注"
}

```

管理后台 - 站内信模版创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|name|string|true|none||模版名称|
|code|string|true|none||模版编码|
|type|integer(int32)|true|none||模版类型，对应 system_notify_template_type 字典|
|nickname|string|true|none||发送人名称|
|content|string|true|none||模版内容|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举|
|remark|string|false|none||备注|

<h2 id="tocS_NoticeSaveReqVO">NoticeSaveReqVO</h2>

<a id="schemanoticesavereqvo"></a>
<a id="schema_NoticeSaveReqVO"></a>
<a id="tocSnoticesavereqvo"></a>
<a id="tocsnoticesavereqvo"></a>

```json
{
  "id": 1024,
  "title": "小博主",
  "type": "小博主",
  "content": "半生编码",
  "status": 1
}

```

管理后台 - 通知公告创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||岗位公告编号|
|title|string|true|none||公告标题|
|type|integer(int32)|true|none||公告类型|
|content|string|true|none||公告内容|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举类|

<h2 id="tocS_MenuSaveVO">MenuSaveVO</h2>

<a id="schemamenusavevo"></a>
<a id="schema_MenuSaveVO"></a>
<a id="tocSmenusavevo"></a>
<a id="tocsmenusavevo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "permission": "sys:menu:add",
  "type": 1,
  "sort": 1024,
  "parentId": 1024,
  "path": "post",
  "icon": "/menu/list",
  "component": "system/post/index",
  "componentName": "SystemUser",
  "status": 1,
  "visible": false,
  "keepAlive": false,
  "alwaysShow": false
}

```

管理后台 - 菜单创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||菜单编号|
|name|string|true|none||菜单名称|
|permission|string|false|none||权限标识,仅菜单类型为按钮时，才需要传递|
|type|integer(int32)|true|none||类型，参见 MenuTypeEnum 枚举类|
|sort|integer(int32)|true|none||显示顺序|
|parentId|integer(int64)|true|none||父菜单 ID|
|path|string|false|none||路由地址,仅菜单类型为菜单或者目录时，才需要传|
|icon|string|false|none||菜单图标,仅菜单类型为菜单或者目录时，才需要传|
|component|string|false|none||组件路径,仅菜单类型为菜单时，才需要传|
|componentName|string|false|none||组件名|
|status|integer(int32)|true|none||状态,见 CommonStatusEnum 枚举|
|visible|boolean|false|none||是否可见|
|keepAlive|boolean|false|none||是否缓存|
|alwaysShow|boolean|false|none||是否总是显示|

<h2 id="tocS_MailTemplateSaveReqVO">MailTemplateSaveReqVO</h2>

<a id="schemamailtemplatesavereqvo"></a>
<a id="schema_MailTemplateSaveReqVO"></a>
<a id="tocSmailtemplatesavereqvo"></a>
<a id="tocsmailtemplatesavereqvo"></a>

```json
{
  "id": 1024,
  "name": "测试名字",
  "code": "test",
  "accountId": 1,
  "nickname": "芋头",
  "title": "注册成功",
  "content": "你好，注册成功啦",
  "status": 1,
  "remark": "奥特曼"
}

```

管理后台 - 邮件模版创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||编号|
|name|string|true|none||模版名称|
|code|string|true|none||模版编号|
|accountId|integer(int64)|true|none||发送的邮箱账号编号|
|nickname|string|false|none||发送人名称|
|title|string|true|none||标题|
|content|string|true|none||内容|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举|
|remark|string|false|none||备注|

<h2 id="tocS_MailAccountSaveReqVO">MailAccountSaveReqVO</h2>

<a id="schemamailaccountsavereqvo"></a>
<a id="schema_MailAccountSaveReqVO"></a>
<a id="tocSmailaccountsavereqvo"></a>
<a id="tocsmailaccountsavereqvo"></a>

```json
{
  "id": 1024,
  "mail": "<EMAIL>",
  "username": "yudao",
  "password": 123456,
  "host": "www.iocoder.cn",
  "port": 80,
  "sslEnable": true,
  "starttlsEnable": true
}

```

管理后台 - 邮箱账号创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||编号|
|mail|string|true|none||邮箱|
|username|string|true|none||用户名|
|password|string|true|none||密码|
|host|string|true|none||SMTP 服务器域名|
|port|integer(int32)|true|none||SMTP 服务器端口|
|sslEnable|boolean|true|none||是否开启 ssl|
|starttlsEnable|boolean|true|none||是否开启 starttls|

<h2 id="tocS_DictTypeSaveReqVO">DictTypeSaveReqVO</h2>

<a id="schemadicttypesavereqvo"></a>
<a id="schema_DictTypeSaveReqVO"></a>
<a id="tocSdicttypesavereqvo"></a>
<a id="tocsdicttypesavereqvo"></a>

```json
{
  "id": 1024,
  "name": "性别",
  "type": "sys_common_sex",
  "status": 1,
  "remark": "快乐的备注"
}

```

管理后台 - 字典类型创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||字典类型编号|
|name|string|true|none||字典名称|
|type|string|true|none||字典类型|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举类|
|remark|string|false|none||备注|

<h2 id="tocS_DictDataSaveReqVO">DictDataSaveReqVO</h2>

<a id="schemadictdatasavereqvo"></a>
<a id="schema_DictDataSaveReqVO"></a>
<a id="tocSdictdatasavereqvo"></a>
<a id="tocsdictdatasavereqvo"></a>

```json
{
  "id": 1024,
  "sort": 1024,
  "label": "芋道",
  "value": "iocoder",
  "dictType": "sys_common_sex",
  "status": 1,
  "colorType": "default",
  "cssClass": "btn-visible",
  "remark": "我是一个角色"
}

```

管理后台 - 字典数据创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||字典数据编号|
|sort|integer(int32)|true|none||显示顺序|
|label|string|true|none||字典标签|
|value|string|true|none||字典值|
|dictType|string|true|none||字典类型|
|status|integer(int32)|true|none||状态,见 CommonStatusEnum 枚举|
|colorType|string|false|none||颜色类型,default、primary、success、info、warning、danger|
|cssClass|string|false|none||css 样式|
|remark|string|false|none||备注|

<h2 id="tocS_DeptSaveReqVO">DeptSaveReqVO</h2>

<a id="schemadeptsavereqvo"></a>
<a id="schema_DeptSaveReqVO"></a>
<a id="tocSdeptsavereqvo"></a>
<a id="tocsdeptsavereqvo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "parentId": 1024,
  "sort": 1024,
  "leaderUserId": 2048,
  "phone": 15601691000,
  "email": "<EMAIL>",
  "status": 1
}

```

管理后台 - 部门创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||部门编号|
|name|string|true|none||部门名称|
|parentId|integer(int64)|false|none||父部门 ID|
|sort|integer(int32)|true|none||显示顺序|
|leaderUserId|integer(int64)|false|none||负责人的用户编号|
|phone|string|false|none||联系电话|
|email|string|false|none||邮箱|
|status|integer(int32)|true|none||状态,见 CommonStatusEnum 枚举|

<h2 id="tocS_JobSaveReqVO">JobSaveReqVO</h2>

<a id="schemajobsavereqvo"></a>
<a id="schema_JobSaveReqVO"></a>
<a id="tocSjobsavereqvo"></a>
<a id="tocsjobsavereqvo"></a>

```json
{
  "id": 1024,
  "name": "测试任务",
  "handlerName": "sysUserSessionTimeoutJob",
  "handlerParam": "yudao",
  "cronExpression": "0/10 * * * * ? *",
  "retryCount": 3,
  "retryInterval": 1000,
  "monitorTimeout": 1000
}

```

管理后台 - 定时任务创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||任务编号|
|name|string|true|none||任务名称|
|handlerName|string|true|none||处理器的名字|
|handlerParam|string|false|none||处理器的参数|
|cronExpression|string|true|none||CRON 表达式|
|retryCount|integer(int32)|true|none||重试次数|
|retryInterval|integer(int32)|true|none||重试间隔|
|monitorTimeout|integer(int32)|false|none||监控超时时间|

<h2 id="tocS_Demo03CourseDO">Demo03CourseDO</h2>

<a id="schemademo03coursedo"></a>
<a id="schema_Demo03CourseDO"></a>
<a id="tocSdemo03coursedo"></a>
<a id="tocsdemo03coursedo"></a>

```json
{
  "createTime": "2019-08-24T14:15:22Z",
  "updateTime": "2019-08-24T14:15:22Z",
  "creator": "string",
  "updater": "string",
  "deleted": true,
  "id": 0,
  "studentId": 0,
  "name": "string",
  "score": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createTime|string(date-time)|false|none||none|
|updateTime|string(date-time)|false|none||none|
|creator|string|false|none||none|
|updater|string|false|none||none|
|deleted|boolean|false|none||none|
|id|integer(int64)|false|none||none|
|studentId|integer(int64)|false|none||none|
|name|string|false|none||none|
|score|integer(int32)|false|none||none|

<h2 id="tocS_Demo03GradeDO">Demo03GradeDO</h2>

<a id="schemademo03gradedo"></a>
<a id="schema_Demo03GradeDO"></a>
<a id="tocSdemo03gradedo"></a>
<a id="tocsdemo03gradedo"></a>

```json
{
  "createTime": "2019-08-24T14:15:22Z",
  "updateTime": "2019-08-24T14:15:22Z",
  "creator": "string",
  "updater": "string",
  "deleted": true,
  "id": 0,
  "studentId": 0,
  "name": "string",
  "teacher": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createTime|string(date-time)|false|none||none|
|updateTime|string(date-time)|false|none||none|
|creator|string|false|none||none|
|updater|string|false|none||none|
|deleted|boolean|false|none||none|
|id|integer(int64)|false|none||none|
|studentId|integer(int64)|false|none||none|
|name|string|false|none||none|
|teacher|string|false|none||none|

<h2 id="tocS_Demo03StudentNormalSaveReqVO">Demo03StudentNormalSaveReqVO</h2>

<a id="schemademo03studentnormalsavereqvo"></a>
<a id="schema_Demo03StudentNormalSaveReqVO"></a>
<a id="tocSdemo03studentnormalsavereqvo"></a>
<a id="tocsdemo03studentnormalsavereqvo"></a>

```json
{
  "id": 8525,
  "name": "芋艿",
  "sex": 0,
  "birthday": "2019-08-24T14:15:22Z",
  "description": "随便",
  "demo03Courses": [
    {
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z",
      "creator": "string",
      "updater": "string",
      "deleted": true,
      "id": 0,
      "studentId": 0,
      "name": "string",
      "score": 0
    }
  ],
  "demo03Grade": {
    "createTime": "2019-08-24T14:15:22Z",
    "updateTime": "2019-08-24T14:15:22Z",
    "creator": "string",
    "updater": "string",
    "deleted": true,
    "id": 0,
    "studentId": 0,
    "name": "string",
    "teacher": "string"
  }
}

```

管理后台 - 学生新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|sex|integer(int32)|true|none||性别|
|birthday|string(date-time)|true|none||出生日期|
|description|string|true|none||简介|
|demo03Courses|[[Demo03CourseDO](#schemademo03coursedo)]|false|none||学生课程列表|
|demo03Grade|[Demo03GradeDO](#schemademo03gradedo)|false|none||学生班级|

<h2 id="tocS_Demo03StudentInnerSaveReqVO">Demo03StudentInnerSaveReqVO</h2>

<a id="schemademo03studentinnersavereqvo"></a>
<a id="schema_Demo03StudentInnerSaveReqVO"></a>
<a id="tocSdemo03studentinnersavereqvo"></a>
<a id="tocsdemo03studentinnersavereqvo"></a>

```json
{
  "id": 8525,
  "name": "芋艿",
  "sex": 0,
  "birthday": "2019-08-24T14:15:22Z",
  "description": "随便",
  "demo03Courses": [
    {
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z",
      "creator": "string",
      "updater": "string",
      "deleted": true,
      "id": 0,
      "studentId": 0,
      "name": "string",
      "score": 0
    }
  ],
  "demo03Grade": {
    "createTime": "2019-08-24T14:15:22Z",
    "updateTime": "2019-08-24T14:15:22Z",
    "creator": "string",
    "updater": "string",
    "deleted": true,
    "id": 0,
    "studentId": 0,
    "name": "string",
    "teacher": "string"
  }
}

```

管理后台 - 学生新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|sex|integer(int32)|true|none||性别|
|birthday|string(date-time)|true|none||出生日期|
|description|string|true|none||简介|
|demo03Courses|[[Demo03CourseDO](#schemademo03coursedo)]|false|none||学生课程列表|
|demo03Grade|[Demo03GradeDO](#schemademo03gradedo)|false|none||学生班级|

<h2 id="tocS_Demo03StudentErpSaveReqVO">Demo03StudentErpSaveReqVO</h2>

<a id="schemademo03studenterpsavereqvo"></a>
<a id="schema_Demo03StudentErpSaveReqVO"></a>
<a id="tocSdemo03studenterpsavereqvo"></a>
<a id="tocsdemo03studenterpsavereqvo"></a>

```json
{
  "id": 8525,
  "name": "芋艿",
  "sex": 0,
  "birthday": "2019-08-24T14:15:22Z",
  "description": "随便"
}

```

管理后台 - 学生新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|sex|integer(int32)|true|none||性别|
|birthday|string(date-time)|true|none||出生日期|
|description|string|true|none||简介|

<h2 id="tocS_Demo02CategorySaveReqVO">Demo02CategorySaveReqVO</h2>

<a id="schemademo02categorysavereqvo"></a>
<a id="schema_Demo02CategorySaveReqVO"></a>
<a id="tocSdemo02categorysavereqvo"></a>
<a id="tocsdemo02categorysavereqvo"></a>

```json
{
  "id": 10304,
  "name": "芋艿",
  "parentId": 6080
}

```

管理后台 - 示例分类新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|parentId|integer(int64)|true|none||父级编号|

<h2 id="tocS_Demo01ContactSaveReqVO">Demo01ContactSaveReqVO</h2>

<a id="schemademo01contactsavereqvo"></a>
<a id="schema_Demo01ContactSaveReqVO"></a>
<a id="tocSdemo01contactsavereqvo"></a>
<a id="tocsdemo01contactsavereqvo"></a>

```json
{
  "id": 21555,
  "name": "张三",
  "sex": 1,
  "birthday": "2019-08-24T14:15:22Z",
  "description": "你说的对",
  "avatar": "string"
}

```

管理后台 - 示例联系人新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|sex|integer(int32)|true|none||性别|
|birthday|string(date-time)|true|none||出生年|
|description|string|true|none||简介|
|avatar|string|false|none||头像|

<h2 id="tocS_DataSourceConfigSaveReqVO">DataSourceConfigSaveReqVO</h2>

<a id="schemadatasourceconfigsavereqvo"></a>
<a id="schema_DataSourceConfigSaveReqVO"></a>
<a id="tocSdatasourceconfigsavereqvo"></a>
<a id="tocsdatasourceconfigsavereqvo"></a>

```json
{
  "id": 1024,
  "name": "test",
  "url": "*****************************************",
  "username": "root",
  "password": 123456
}

```

管理后台 - 数据源配置创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键编号|
|name|string|true|none||数据源名称|
|url|string|true|none||数据源连接|
|username|string|true|none||用户名|
|password|string|true|none||密码|

<h2 id="tocS_ConfigSaveReqVO">ConfigSaveReqVO</h2>

<a id="schemaconfigsavereqvo"></a>
<a id="schema_ConfigSaveReqVO"></a>
<a id="tocSconfigsavereqvo"></a>
<a id="tocsconfigsavereqvo"></a>

```json
{
  "id": 1024,
  "category": "biz",
  "name": "数据库名",
  "key": "yunai.db.username",
  "value": 1024,
  "visible": true,
  "remark": "备注一下很帅气！"
}

```

管理后台 - 参数配置创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||参数配置序号|
|category|string|true|none||参数分组|
|name|string|true|none||参数名称|
|key|string|true|none||参数键名|
|value|string|true|none||参数键值|
|visible|boolean|true|none||是否可见|
|remark|string|false|none||备注|

<h2 id="tocS_CodegenColumnSaveReqVO">CodegenColumnSaveReqVO</h2>

<a id="schemacodegencolumnsavereqvo"></a>
<a id="schema_CodegenColumnSaveReqVO"></a>
<a id="tocScodegencolumnsavereqvo"></a>
<a id="tocscodegencolumnsavereqvo"></a>

```json
{
  "id": 1,
  "tableId": 1,
  "columnName": "user_age",
  "dataType": "int(11)",
  "columnComment": "年龄",
  "nullable": true,
  "primaryKey": false,
  "ordinalPosition": 10,
  "javaType": "userAge",
  "javaField": "Integer",
  "dictType": "sys_gender",
  "example": 1024,
  "createOperation": true,
  "updateOperation": false,
  "listOperation": true,
  "listOperationCondition": "LIKE",
  "listOperationResult": true,
  "htmlType": "input"
}

```

管理后台 - 代码生成字段定义创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|tableId|integer(int64)|true|none||表编号|
|columnName|string|true|none||字段名|
|dataType|string|true|none||字段类型|
|columnComment|string|true|none||字段描述|
|nullable|boolean|true|none||是否允许为空|
|primaryKey|boolean|true|none||是否主键|
|ordinalPosition|integer(int32)|true|none||排序|
|javaType|string|true|none||Java 属性类型|
|javaField|string|true|none||Java 属性名|
|dictType|string|false|none||字典类型|
|example|string|false|none||数据示例|
|createOperation|boolean|true|none||是否为 Create 创建操作的字段|
|updateOperation|boolean|true|none||是否为 Update 更新操作的字段|
|listOperation|boolean|true|none||是否为 List 查询操作的字段|
|listOperationCondition|string|true|none||List 查询操作的条件类型，参见 CodegenColumnListConditionEnum 枚举|
|listOperationResult|boolean|true|none||是否为 List 查询操作的返回字段|
|htmlType|string|true|none||显示类型|

<h2 id="tocS_CodegenTableSaveReqVO">CodegenTableSaveReqVO</h2>

<a id="schemacodegentablesavereqvo"></a>
<a id="schema_CodegenTableSaveReqVO"></a>
<a id="tocScodegentablesavereqvo"></a>
<a id="tocscodegentablesavereqvo"></a>

```json
{
  "id": 1,
  "scene": 1,
  "tableName": "yudao",
  "tableComment": "芋道",
  "remark": "我是备注",
  "moduleName": "system",
  "businessName": "codegen",
  "className": "CodegenTable",
  "classComment": "代码生成器的表定义",
  "author": "芋道源码",
  "templateType": 1,
  "frontType": 20,
  "parentMenuId": 1024,
  "masterTableId": 2048,
  "subJoinColumnId": 4096,
  "subJoinMany": 4096,
  "treeParentColumnId": 8192,
  "treeNameColumnId": 16384
}

```

管理后台 - 代码生成表定义创建/修改 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|scene|integer(int32)|true|none||生成场景，参见 CodegenSceneEnum 枚举|
|tableName|string|true|none||表名称|
|tableComment|string|true|none||表描述|
|remark|string|false|none||备注|
|moduleName|string|true|none||模块名|
|businessName|string|true|none||业务名|
|className|string|true|none||类名称|
|classComment|string|true|none||类描述|
|author|string|true|none||作者|
|templateType|integer(int32)|true|none||模板类型，参见 CodegenTemplateTypeEnum 枚举|
|frontType|integer(int32)|true|none||前端类型，参见 CodegenFrontTypeEnum 枚举|
|parentMenuId|integer(int64)|false|none||父菜单编号|
|masterTableId|integer(int64)|false|none||主表的编号|
|subJoinColumnId|integer(int64)|false|none||子表关联主表的字段编号|
|subJoinMany|boolean|false|none||主表与子表是否一对多|
|treeParentColumnId|integer(int64)|false|none||树表的父字段编号|
|treeNameColumnId|integer(int64)|false|none||树表的名字字段编号|

<h2 id="tocS_CodegenUpdateReqVO">CodegenUpdateReqVO</h2>

<a id="schemacodegenupdatereqvo"></a>
<a id="schema_CodegenUpdateReqVO"></a>
<a id="tocScodegenupdatereqvo"></a>
<a id="tocscodegenupdatereqvo"></a>

```json
{
  "table": {
    "id": 1,
    "scene": 1,
    "tableName": "yudao",
    "tableComment": "芋道",
    "remark": "我是备注",
    "moduleName": "system",
    "businessName": "codegen",
    "className": "CodegenTable",
    "classComment": "代码生成器的表定义",
    "author": "芋道源码",
    "templateType": 1,
    "frontType": 20,
    "parentMenuId": 1024,
    "masterTableId": 2048,
    "subJoinColumnId": 4096,
    "subJoinMany": 4096,
    "treeParentColumnId": 8192,
    "treeNameColumnId": 16384
  },
  "columns": [
    {
      "id": 1,
      "tableId": 1,
      "columnName": "user_age",
      "dataType": "int(11)",
      "columnComment": "年龄",
      "nullable": true,
      "primaryKey": false,
      "ordinalPosition": 10,
      "javaType": "userAge",
      "javaField": "Integer",
      "dictType": "sys_gender",
      "example": 1024,
      "createOperation": true,
      "updateOperation": false,
      "listOperation": true,
      "listOperationCondition": "LIKE",
      "listOperationResult": true,
      "htmlType": "input"
    }
  ]
}

```

管理后台 - 代码生成表和字段的修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|table|[CodegenTableSaveReqVO](#schemacodegentablesavereqvo)|true|none||管理后台 - 代码生成表定义创建/修改 Response VO|
|columns|[[CodegenColumnSaveReqVO](#schemacodegencolumnsavereqvo)]|true|none||[管理后台 - 代码生成字段定义创建/修改 Request VO]|

<h2 id="tocS_FindByIdsQueryPayload">FindByIdsQueryPayload</h2>

<a id="schemafindbyidsquerypayload"></a>
<a id="schema_FindByIdsQueryPayload"></a>
<a id="tocSfindbyidsquerypayload"></a>
<a id="tocsfindbyidsquerypayload"></a>

```json
{
  "ids": [
    "string"
  ],
  "uniqueField": "string",
  "targetFields": [
    "string"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ids|[string]|false|none||none|
|uniqueField|string|false|none||none|
|targetFields|[string]|false|none||none|

<h2 id="tocS_CommonResultUserImportRespVO">CommonResultUserImportRespVO</h2>

<a id="schemacommonresultuserimportrespvo"></a>
<a id="schema_CommonResultUserImportRespVO"></a>
<a id="tocScommonresultuserimportrespvo"></a>
<a id="tocscommonresultuserimportrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "createUsernames": [
      "string"
    ],
    "updateUsernames": [
      "string"
    ],
    "failureUsernames": {
      "property1": "string",
      "property2": "string"
    }
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[UserImportRespVO](#schemauserimportrespvo)|false|none||管理后台 - 用户导入 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_UserImportRespVO">UserImportRespVO</h2>

<a id="schemauserimportrespvo"></a>
<a id="schema_UserImportRespVO"></a>
<a id="tocSuserimportrespvo"></a>
<a id="tocsuserimportrespvo"></a>

```json
{
  "createUsernames": [
    "string"
  ],
  "updateUsernames": [
    "string"
  ],
  "failureUsernames": {
    "property1": "string",
    "property2": "string"
  }
}

```

管理后台 - 用户导入 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createUsernames|[string]|true|none||创建成功的用户名数组|
|updateUsernames|[string]|true|none||更新成功的用户名数组|
|failureUsernames|object|true|none||导入失败的用户集合，key 为用户名，value 为失败原因|
|» **additionalProperties**|string|false|none||none|

<h2 id="tocS_SocialUserBindReqVO">SocialUserBindReqVO</h2>

<a id="schemasocialuserbindreqvo"></a>
<a id="schema_SocialUserBindReqVO"></a>
<a id="tocSsocialuserbindreqvo"></a>
<a id="tocssocialuserbindreqvo"></a>

```json
{
  "type": 10,
  "code": 1024,
  "state": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}

```

管理后台 - 社交绑定 Request VO，使用 code 授权码

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|integer(int32)|true|none||社交平台的类型，参见 UserSocialTypeEnum 枚举值|
|code|string|true|none||授权码|
|state|string|true|none||state|

<h2 id="tocS_SocialWxaSubscribeMessageSendReqDTO">SocialWxaSubscribeMessageSendReqDTO</h2>

<a id="schemasocialwxasubscribemessagesendreqdto"></a>
<a id="schema_SocialWxaSubscribeMessageSendReqDTO"></a>
<a id="tocSsocialwxasubscribemessagesendreqdto"></a>
<a id="tocssocialwxasubscribemessagesendreqdto"></a>

```json
{
  "userId": 0,
  "userType": 0,
  "templateTitle": "string",
  "page": "string",
  "messages": {
    "property1": "string",
    "property2": "string"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|true|none||none|
|userType|integer(int32)|true|none||none|
|templateTitle|string|true|none||none|
|page|string|false|none||none|
|messages|object|false|none||none|
|» **additionalProperties**|string|false|none||none|

<h2 id="tocS_SmsTemplateSendReqVO">SmsTemplateSendReqVO</h2>

<a id="schemasmstemplatesendreqvo"></a>
<a id="schema_SmsTemplateSendReqVO"></a>
<a id="tocSsmstemplatesendreqvo"></a>
<a id="tocssmstemplatesendreqvo"></a>

```json
{
  "mobile": ***********,
  "templateCode": "test_01",
  "templateParams": {
    "property1": {},
    "property2": {}
  }
}

```

管理后台 - 短信模板的发送 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|mobile|string|true|none||手机号|
|templateCode|string|true|none||模板编码|
|templateParams|object|false|none||模板参数|
|» **additionalProperties**|object|false|none||none|

<h2 id="tocS_PermissionAssignUserRoleReqVO">PermissionAssignUserRoleReqVO</h2>

<a id="schemapermissionassignuserrolereqvo"></a>
<a id="schema_PermissionAssignUserRoleReqVO"></a>
<a id="tocSpermissionassignuserrolereqvo"></a>
<a id="tocspermissionassignuserrolereqvo"></a>

```json
{
  "userId": 1,
  "roleIds": "1,3,5"
}

```

管理后台 - 赋予用户角色 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|true|none||用户编号|
|roleIds|[integer]|false|none||角色编号列表|

<h2 id="tocS_PermissionAssignRoleMenuReqVO">PermissionAssignRoleMenuReqVO</h2>

<a id="schemapermissionassignrolemenureqvo"></a>
<a id="schema_PermissionAssignRoleMenuReqVO"></a>
<a id="tocSpermissionassignrolemenureqvo"></a>
<a id="tocspermissionassignrolemenureqvo"></a>

```json
{
  "roleId": 1,
  "menuIds": "1,3,5"
}

```

管理后台 - 赋予角色菜单 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roleId|integer(int64)|true|none||角色编号|
|menuIds|[integer]|false|none||菜单编号列表|

<h2 id="tocS_PermissionAssignRoleDataScopeReqVO">PermissionAssignRoleDataScopeReqVO</h2>

<a id="schemapermissionassignroledatascopereqvo"></a>
<a id="schema_PermissionAssignRoleDataScopeReqVO"></a>
<a id="tocSpermissionassignroledatascopereqvo"></a>
<a id="tocspermissionassignroledatascopereqvo"></a>

```json
{
  "roleId": 1,
  "dataScope": 1,
  "dataScopeDeptIds": "1,3,5"
}

```

管理后台 - 赋予角色数据权限 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|roleId|integer(int64)|true|none||角色编号|
|dataScope|integer(int32)|true|none||数据范围，参见 DataScopeEnum 枚举类|
|dataScopeDeptIds|[integer]|false|none||部门编号列表，只有范围类型为 DEPT_CUSTOM 时，该字段才需要|

<h2 id="tocS_CommonResultOAuth2OpenAccessTokenRespVO">CommonResultOAuth2OpenAccessTokenRespVO</h2>

<a id="schemacommonresultoauth2openaccesstokenrespvo"></a>
<a id="schema_CommonResultOAuth2OpenAccessTokenRespVO"></a>
<a id="tocScommonresultoauth2openaccesstokenrespvo"></a>
<a id="tocscommonresultoauth2openaccesstokenrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "scope": "user_info",
    "access_token": "tudou",
    "refresh_token": "nice",
    "token_type": "bearer",
    "expires_in": 42430
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[OAuth2OpenAccessTokenRespVO](#schemaoauth2openaccesstokenrespvo)|false|none||管理后台 - 【开放接口】访问令牌 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_OAuth2OpenAccessTokenRespVO">OAuth2OpenAccessTokenRespVO</h2>

<a id="schemaoauth2openaccesstokenrespvo"></a>
<a id="schema_OAuth2OpenAccessTokenRespVO"></a>
<a id="tocSoauth2openaccesstokenrespvo"></a>
<a id="tocsoauth2openaccesstokenrespvo"></a>

```json
{
  "scope": "user_info",
  "access_token": "tudou",
  "refresh_token": "nice",
  "token_type": "bearer",
  "expires_in": 42430
}

```

管理后台 - 【开放接口】访问令牌 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|scope|string|false|none||授权范围,如果多个授权范围，使用空格分隔|
|access_token|string|true|none||访问令牌|
|refresh_token|string|true|none||刷新令牌|
|token_type|string|true|none||令牌类型|
|expires_in|integer(int64)|true|none||过期时间,单位：秒|

<h2 id="tocS_CommonResultOAuth2OpenCheckTokenRespVO">CommonResultOAuth2OpenCheckTokenRespVO</h2>

<a id="schemacommonresultoauth2openchecktokenrespvo"></a>
<a id="schema_CommonResultOAuth2OpenCheckTokenRespVO"></a>
<a id="tocScommonresultoauth2openchecktokenrespvo"></a>
<a id="tocscommonresultoauth2openchecktokenrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "scopes": "user_info",
    "exp": 1593092157,
    "user_id": 666,
    "user_type": 2,
    "tenant_id": 1024,
    "client_id": "car",
    "access_token": "tudou"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[OAuth2OpenCheckTokenRespVO](#schemaoauth2openchecktokenrespvo)|false|none||管理后台 - 【开放接口】校验令牌 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_OAuth2OpenCheckTokenRespVO">OAuth2OpenCheckTokenRespVO</h2>

<a id="schemaoauth2openchecktokenrespvo"></a>
<a id="schema_OAuth2OpenCheckTokenRespVO"></a>
<a id="tocSoauth2openchecktokenrespvo"></a>
<a id="tocsoauth2openchecktokenrespvo"></a>

```json
{
  "scopes": "user_info",
  "exp": 1593092157,
  "user_id": 666,
  "user_type": 2,
  "tenant_id": 1024,
  "client_id": "car",
  "access_token": "tudou"
}

```

管理后台 - 【开放接口】校验令牌 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|scopes|[string]|true|none||授权范围|
|exp|integer(int64)|true|none||过期时间，时间戳 / 1000，即单位：秒|
|user_id|integer(int64)|true|none||用户编号|
|user_type|integer(int32)|true|none||用户类型，参见 UserTypeEnum 枚举|
|tenant_id|integer(int64)|true|none||租户编号|
|client_id|string|true|none||客户端编号|
|access_token|string|true|none||访问令牌|

<h2 id="tocS_NotifyTemplateSendReqVO">NotifyTemplateSendReqVO</h2>

<a id="schemanotifytemplatesendreqvo"></a>
<a id="schema_NotifyTemplateSendReqVO"></a>
<a id="tocSnotifytemplatesendreqvo"></a>
<a id="tocsnotifytemplatesendreqvo"></a>

```json
{
  "userId": "01",
  "userType": 1,
  "templateCode": "01",
  "templateParams": {
    "property1": {},
    "property2": {}
  }
}

```

管理后台 - 站内信模板的发送 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|true|none||用户id|
|userType|integer(int32)|true|none||用户类型|
|templateCode|string|true|none||模板编码|
|templateParams|object|false|none||模板参数|
|» **additionalProperties**|object|false|none||none|

<h2 id="tocS_MailTemplateSendReqVO">MailTemplateSendReqVO</h2>

<a id="schemamailtemplatesendreqvo"></a>
<a id="schema_MailTemplateSendReqVO"></a>
<a id="tocSmailtemplatesendreqvo"></a>
<a id="tocsmailtemplatesendreqvo"></a>

```json
{
  "mail": "<EMAIL>",
  "templateCode": "test_01",
  "templateParams": {
    "property1": {},
    "property2": {}
  }
}

```

管理后台 - 邮件发送 Req VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|mail|string|true|none||接收邮箱|
|templateCode|string|true|none||模板编码|
|templateParams|object|false|none||模板参数|
|» **additionalProperties**|object|false|none||none|

<h2 id="tocS_CaptchaVO">CaptchaVO</h2>

<a id="schemacaptchavo"></a>
<a id="schema_CaptchaVO"></a>
<a id="tocScaptchavo"></a>
<a id="tocscaptchavo"></a>

```json
{
  "captchaId": "string",
  "projectCode": "string",
  "captchaType": "string",
  "captchaOriginalPath": "string",
  "captchaFontType": "string",
  "captchaFontSize": 0,
  "secretKey": "string",
  "originalImageBase64": "string",
  "point": {
    "secretKey": "string",
    "x": 0,
    "y": 0
  },
  "jigsawImageBase64": "string",
  "wordList": [
    "string"
  ],
  "pointList": [
    {
      "x": 0.1,
      "y": 0.1
    }
  ],
  "pointJson": "string",
  "token": "string",
  "result": true,
  "captchaVerification": "string",
  "clientUid": "string",
  "ts": 0,
  "browserInfo": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|captchaId|string|false|none||none|
|projectCode|string|false|none||none|
|captchaType|string|false|none||none|
|captchaOriginalPath|string|false|none||none|
|captchaFontType|string|false|none||none|
|captchaFontSize|integer(int32)|false|none||none|
|secretKey|string|false|none||none|
|originalImageBase64|string|false|none||none|
|point|[PointVO](#schemapointvo)|false|none||none|
|jigsawImageBase64|string|false|none||none|
|wordList|[string]|false|none||none|
|pointList|[object]|false|none||none|
|» x|number(double)|false|none||none|
|» y|number(double)|false|none||none|
|pointJson|string|false|none||none|
|token|string|false|none||none|
|result|boolean|false|none||none|
|captchaVerification|string|false|none||none|
|clientUid|string|false|none||none|
|ts|integer(int64)|false|none||none|
|browserInfo|string|false|none||none|

<h2 id="tocS_PointVO">PointVO</h2>

<a id="schemapointvo"></a>
<a id="schema_PointVO"></a>
<a id="tocSpointvo"></a>
<a id="tocspointvo"></a>

```json
{
  "secretKey": "string",
  "x": 0,
  "y": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|secretKey|string|false|none||none|
|x|integer(int32)|false|none||none|
|y|integer(int32)|false|none||none|

<h2 id="tocS_ResponseModel">ResponseModel</h2>

<a id="schemaresponsemodel"></a>
<a id="schema_ResponseModel"></a>
<a id="tocSresponsemodel"></a>
<a id="tocsresponsemodel"></a>

```json
{
  "repCode": "string",
  "repMsg": "string",
  "repData": {},
  "repCodeEnum": "SUCCESS",
  "success": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|repCode|string|false|none||none|
|repMsg|string|false|none||none|
|repData|object|false|none||none|
|repCodeEnum|string|false|write-only||none|
|success|boolean|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|repCodeEnum|SUCCESS|
|repCodeEnum|ERROR|
|repCodeEnum|EXCEPTION|
|repCodeEnum|BLANK_ERROR|
|repCodeEnum|NULL_ERROR|
|repCodeEnum|NOT_NULL_ERROR|
|repCodeEnum|NOT_EXIST_ERROR|
|repCodeEnum|EXIST_ERROR|
|repCodeEnum|PARAM_TYPE_ERROR|
|repCodeEnum|PARAM_FORMAT_ERROR|
|repCodeEnum|API_CAPTCHA_INVALID|
|repCodeEnum|API_CAPTCHA_COORDINATE_ERROR|
|repCodeEnum|API_CAPTCHA_ERROR|
|repCodeEnum|API_CAPTCHA_BASEMAP_NULL|
|repCodeEnum|API_REQ_LIMIT_GET_ERROR|
|repCodeEnum|API_REQ_INVALID|
|repCodeEnum|API_REQ_LOCK_GET_ERROR|
|repCodeEnum|API_REQ_LIMIT_CHECK_ERROR|
|repCodeEnum|API_REQ_LIMIT_VERIFY_ERROR|

<h2 id="tocS_AuthSocialLoginReqVO">AuthSocialLoginReqVO</h2>

<a id="schemaauthsocialloginreqvo"></a>
<a id="schema_AuthSocialLoginReqVO"></a>
<a id="tocSauthsocialloginreqvo"></a>
<a id="tocsauthsocialloginreqvo"></a>

```json
{
  "type": 10,
  "code": 1024,
  "state": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}

```

管理后台 - 社交绑定登录 Request VO，使用 code 授权码 + 账号密码

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|integer(int32)|true|none||社交平台的类型，参见 UserSocialTypeEnum 枚举值|
|code|string|true|none||授权码|
|state|string|true|none||state|

<h2 id="tocS_AuthLoginRespVO">AuthLoginRespVO</h2>

<a id="schemaauthloginrespvo"></a>
<a id="schema_AuthLoginRespVO"></a>
<a id="tocSauthloginrespvo"></a>
<a id="tocsauthloginrespvo"></a>

```json
{
  "userId": 1024,
  "accessToken": "happy",
  "refreshToken": "nice",
  "expiresTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 登录 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|true|none||用户编号|
|accessToken|string|true|none||访问令牌|
|refreshToken|string|true|none||刷新令牌|
|expiresTime|string(date-time)|true|none||过期时间|

<h2 id="tocS_CommonResultAuthLoginRespVO">CommonResultAuthLoginRespVO</h2>

<a id="schemacommonresultauthloginrespvo"></a>
<a id="schema_CommonResultAuthLoginRespVO"></a>
<a id="tocScommonresultauthloginrespvo"></a>
<a id="tocscommonresultauthloginrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "userId": 1024,
    "accessToken": "happy",
    "refreshToken": "nice",
    "expiresTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[AuthLoginRespVO](#schemaauthloginrespvo)|false|none||管理后台 - 登录 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_AuthSmsLoginReqVO">AuthSmsLoginReqVO</h2>

<a id="schemaauthsmsloginreqvo"></a>
<a id="schema_AuthSmsLoginReqVO"></a>
<a id="tocSauthsmsloginreqvo"></a>
<a id="tocsauthsmsloginreqvo"></a>

```json
{
  "mobile": "yudaoyuanma",
  "code": 1024
}

```

管理后台 - 短信验证码的登录 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|mobile|string|true|none||手机号|
|code|string|true|none||短信验证码|

<h2 id="tocS_AuthSmsSendReqVO">AuthSmsSendReqVO</h2>

<a id="schemaauthsmssendreqvo"></a>
<a id="schema_AuthSmsSendReqVO"></a>
<a id="tocSauthsmssendreqvo"></a>
<a id="tocsauthsmssendreqvo"></a>

```json
{
  "captchaVerification": "PfcH6mgr8tpXuMWFjvW6YVaqrswIuwmWI5dsVZSg7sGpWtDCUbHuDEXl3cFB1+VvCC/rAkSwK8Fad52FSuncVg==",
  "mobile": "yudaoyuanma",
  "scene": 1
}

```

管理后台 - 发送手机验证码 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|captchaVerification|string|true|none||验证码，验证码开启时，需要传递|
|mobile|string|true|none||手机号|
|scene|integer(int32)|true|none||短信场景|

<h2 id="tocS_AuthResetPasswordReqVO">AuthResetPasswordReqVO</h2>

<a id="schemaauthresetpasswordreqvo"></a>
<a id="schema_AuthResetPasswordReqVO"></a>
<a id="tocSauthresetpasswordreqvo"></a>
<a id="tocsauthresetpasswordreqvo"></a>

```json
{
  "password": 1234,
  "mobile": 13312341234,
  "code": 123456
}

```

管理后台 - 短信重置账号密码 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|password|string|true|none||密码|
|mobile|string|true|none||手机号|
|code|string|true|none||手机短信验证码|

<h2 id="tocS_AuthRegisterReqVO">AuthRegisterReqVO</h2>

<a id="schemaauthregisterreqvo"></a>
<a id="schema_AuthRegisterReqVO"></a>
<a id="tocSauthregisterreqvo"></a>
<a id="tocsauthregisterreqvo"></a>

```json
{
  "captchaVerification": "PfcH6mgr8tpXuMWFjvW6YVaqrswIuwmWI5dsVZSg7sGpWtDCUbHuDEXl3cFB1+VvCC/rAkSwK8Fad52FSuncVg==",
  "username": "yudao",
  "nickname": "芋艿",
  "password": 123456
}

```

管理后台 - Register Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|captchaVerification|string|true|none||验证码，验证码开启时，需要传递|
|username|string|true|none||用户账号|
|nickname|string|true|none||用户昵称|
|password|string|true|none||密码|

<h2 id="tocS_AuthLoginReqVO">AuthLoginReqVO</h2>

<a id="schemaauthloginreqvo"></a>
<a id="schema_AuthLoginReqVO"></a>
<a id="tocSauthloginreqvo"></a>
<a id="tocsauthloginreqvo"></a>

```json
{
  "captchaVerification": "PfcH6mgr8tpXuMWFjvW6YVaqrswIuwmWI5dsVZSg7sGpWtDCUbHuDEXl3cFB1+VvCC/rAkSwK8Fad52FSuncVg==",
  "username": "yudaoyuanma",
  "password": "buzhidao",
  "socialType": 10,
  "socialCode": 1024,
  "socialState": "9b2ffbc1-7425-4155-9894-9d5c08541d62",
  "socialCodeValid": true
}

```

管理后台 - 账号密码登录 Request VO，如果登录并绑定社交用户，需要传递 social 开头的参数

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|captchaVerification|string|true|none||验证码，验证码开启时，需要传递|
|username|string|true|none||账号|
|password|string|true|none||密码|
|socialType|integer(int32)|true|none||社交平台的类型，参见 SocialTypeEnum 枚举值|
|socialCode|string|true|none||授权码|
|socialState|string|true|none||state|
|socialCodeValid|boolean|false|none||none|

<h2 id="tocS_CodegenCreateListReqVO">CodegenCreateListReqVO</h2>

<a id="schemacodegencreatelistreqvo"></a>
<a id="schema_CodegenCreateListReqVO"></a>
<a id="tocScodegencreatelistreqvo"></a>
<a id="tocscodegencreatelistreqvo"></a>

```json
{
  "dataSourceConfigId": 1,
  "tableNames": [
    1,
    2,
    3
  ]
}

```

管理后台 - 基于数据库的表结构，创建代码生成器的表和字段定义 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|dataSourceConfigId|integer(int64)|true|none||数据源配置的编号|
|tableNames|[string]|true|none||表名数组|

<h2 id="tocS_CommonResultListLong">CommonResultListLong</h2>

<a id="schemacommonresultlistlong"></a>
<a id="schema_CommonResultListLong"></a>
<a id="tocScommonresultlistlong"></a>
<a id="tocscommonresultlistlong"></a>

```json
{
  "code": 0,
  "data": [
    0
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[integer]|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_AppDictDataRespVO">AppDictDataRespVO</h2>

<a id="schemaappdictdatarespvo"></a>
<a id="schema_AppDictDataRespVO"></a>
<a id="tocSappdictdatarespvo"></a>
<a id="tocsappdictdatarespvo"></a>

```json
{
  "id": 1024,
  "label": "芋道",
  "value": "iocoder",
  "dictType": "sys_common_sex"
}

```

用户 App - 字典数据信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||字典数据编号|
|label|string|true|none||字典标签|
|value|string|true|none||字典值|
|dictType|string|true|none||字典类型|

<h2 id="tocS_CommonResultListAppDictDataRespVO">CommonResultListAppDictDataRespVO</h2>

<a id="schemacommonresultlistappdictdatarespvo"></a>
<a id="schema_CommonResultListAppDictDataRespVO"></a>
<a id="tocScommonresultlistappdictdatarespvo"></a>
<a id="tocscommonresultlistappdictdatarespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "label": "芋道",
      "value": "iocoder",
      "dictType": "sys_common_sex"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[AppDictDataRespVO](#schemaappdictdatarespvo)]|false|none||[用户 App - 字典数据信息 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_AppAreaNodeRespVO">AppAreaNodeRespVO</h2>

<a id="schemaappareanoderespvo"></a>
<a id="schema_AppAreaNodeRespVO"></a>
<a id="tocSappareanoderespvo"></a>
<a id="tocsappareanoderespvo"></a>

```json
{
  "id": 110000,
  "name": "北京"
}

```

用户 App - 地区节点 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int32)|true|none||编号|
|name|string|true|none||名字|

<h2 id="tocS_CommonResultListAppAreaNodeRespVO">CommonResultListAppAreaNodeRespVO</h2>

<a id="schemacommonresultlistappareanoderespvo"></a>
<a id="schema_CommonResultListAppAreaNodeRespVO"></a>
<a id="tocScommonresultlistappareanoderespvo"></a>
<a id="tocscommonresultlistappareanoderespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 110000,
      "name": "北京"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[AppAreaNodeRespVO](#schemaappareanoderespvo)]|false|none||[用户 App - 地区节点 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultUserProfileRespVO">CommonResultUserProfileRespVO</h2>

<a id="schemacommonresultuserprofilerespvo"></a>
<a id="schema_CommonResultUserProfileRespVO"></a>
<a id="tocScommonresultuserprofilerespvo"></a>
<a id="tocscommonresultuserprofilerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "username": "yudao",
    "nickname": "芋艿",
    "email": "<EMAIL>",
    "mobile": ***********,
    "sex": 1,
    "avatar": "https://www.iocoder.cn/xxx.png",
    "loginIp": "***********",
    "loginDate": "时间戳格式",
    "createTime": "时间戳格式",
    "roles": [
      {
        "id": 1024,
        "name": "芋道"
      }
    ],
    "dept": {
      "id": 1024,
      "name": "芋道",
      "parentId": 1024
    },
    "posts": [
      {
        "id": 1024,
        "name": "小土豆"
      }
    ]
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[UserProfileRespVO](#schemauserprofilerespvo)|false|none||管理后台 - 用户个人中心信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_DeptSimpleRespVO">DeptSimpleRespVO</h2>

<a id="schemadeptsimplerespvo"></a>
<a id="schema_DeptSimpleRespVO"></a>
<a id="tocSdeptsimplerespvo"></a>
<a id="tocsdeptsimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "parentId": 1024
}

```

管理后台 - 部门精简信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||部门编号|
|name|string|true|none||部门名称|
|parentId|integer(int64)|true|none||父部门 ID|

<h2 id="tocS_PostSimpleRespVO">PostSimpleRespVO</h2>

<a id="schemapostsimplerespvo"></a>
<a id="schema_PostSimpleRespVO"></a>
<a id="tocSpostsimplerespvo"></a>
<a id="tocspostsimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "小土豆"
}

```

管理后台 - 岗位信息的精简 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||岗位序号|
|name|string|true|none||岗位名称|

<h2 id="tocS_RoleSimpleRespVO">RoleSimpleRespVO</h2>

<a id="schemarolesimplerespvo"></a>
<a id="schema_RoleSimpleRespVO"></a>
<a id="tocSrolesimplerespvo"></a>
<a id="tocsrolesimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "芋道"
}

```

管理后台 - 角色精简信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||角色编号|
|name|string|true|none||角色名称|

<h2 id="tocS_UserProfileRespVO">UserProfileRespVO</h2>

<a id="schemauserprofilerespvo"></a>
<a id="schema_UserProfileRespVO"></a>
<a id="tocSuserprofilerespvo"></a>
<a id="tocsuserprofilerespvo"></a>

```json
{
  "id": 1,
  "username": "yudao",
  "nickname": "芋艿",
  "email": "<EMAIL>",
  "mobile": ***********,
  "sex": 1,
  "avatar": "https://www.iocoder.cn/xxx.png",
  "loginIp": "***********",
  "loginDate": "时间戳格式",
  "createTime": "时间戳格式",
  "roles": [
    {
      "id": 1024,
      "name": "芋道"
    }
  ],
  "dept": {
    "id": 1024,
    "name": "芋道",
    "parentId": 1024
  },
  "posts": [
    {
      "id": 1024,
      "name": "小土豆"
    }
  ]
}

```

管理后台 - 用户个人中心信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||用户编号|
|username|string|true|none||用户账号|
|nickname|string|true|none||用户昵称|
|email|string|false|none||用户邮箱|
|mobile|string|false|none||手机号码|
|sex|integer(int32)|false|none||用户性别，参见 SexEnum 枚举类|
|avatar|string|false|none||用户头像|
|loginIp|string|true|none||最后登录 IP|
|loginDate|string(date-time)|true|none||最后登录时间|
|createTime|string(date-time)|true|none||创建时间|
|roles|[[RoleSimpleRespVO](#schemarolesimplerespvo)]|false|none||[管理后台 - 角色精简信息 Response VO]|
|dept|[DeptSimpleRespVO](#schemadeptsimplerespvo)|false|none||管理后台 - 部门精简信息 Response VO|
|posts|[[PostSimpleRespVO](#schemapostsimplerespvo)]|false|none||[管理后台 - 岗位信息的精简 Response VO]|

<h2 id="tocS_CommonResultPageResultUserRespVO">CommonResultPageResultUserRespVO</h2>

<a id="schemacommonresultpageresultuserrespvo"></a>
<a id="schema_CommonResultPageResultUserRespVO"></a>
<a id="tocScommonresultpageresultuserrespvo"></a>
<a id="tocscommonresultpageresultuserrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "username": "yudao",
        "nickname": "芋艿",
        "remark": "我是一个用户",
        "deptId": "我是一个用户",
        "deptName": "IT 部",
        "postIds": 1,
        "email": "<EMAIL>",
        "mobile": ***********,
        "sex": 1,
        "avatar": "https://www.iocoder.cn/xxx.png",
        "status": 1,
        "loginIp": "***********",
        "loginDate": "时间戳格式",
        "createTime": "时间戳格式"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultUserRespVO](#schemapageresultuserrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultUserRespVO">PageResultUserRespVO</h2>

<a id="schemapageresultuserrespvo"></a>
<a id="schema_PageResultUserRespVO"></a>
<a id="tocSpageresultuserrespvo"></a>
<a id="tocspageresultuserrespvo"></a>

```json
{
  "list": [
    {
      "id": 1,
      "username": "yudao",
      "nickname": "芋艿",
      "remark": "我是一个用户",
      "deptId": "我是一个用户",
      "deptName": "IT 部",
      "postIds": 1,
      "email": "<EMAIL>",
      "mobile": ***********,
      "sex": 1,
      "avatar": "https://www.iocoder.cn/xxx.png",
      "status": 1,
      "loginIp": "***********",
      "loginDate": "时间戳格式",
      "createTime": "时间戳格式"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[UserRespVO](#schemauserrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_UserRespVO">UserRespVO</h2>

<a id="schemauserrespvo"></a>
<a id="schema_UserRespVO"></a>
<a id="tocSuserrespvo"></a>
<a id="tocsuserrespvo"></a>

```json
{
  "id": 1,
  "username": "yudao",
  "nickname": "芋艿",
  "remark": "我是一个用户",
  "deptId": "我是一个用户",
  "deptName": "IT 部",
  "postIds": 1,
  "email": "<EMAIL>",
  "mobile": ***********,
  "sex": 1,
  "avatar": "https://www.iocoder.cn/xxx.png",
  "status": 1,
  "loginIp": "***********",
  "loginDate": "时间戳格式",
  "createTime": "时间戳格式"
}

```

管理后台 - 用户信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||用户编号|
|username|string|true|none||用户账号|
|nickname|string|true|none||用户昵称|
|remark|string|false|none||备注|
|deptId|integer(int64)|false|none||部门ID|
|deptName|string|false|none||部门名称|
|postIds|[integer]|false|none||岗位编号数组|
|email|string|false|none||用户邮箱|
|mobile|string|false|none||手机号码|
|sex|integer(int32)|false|none||用户性别，参见 SexEnum 枚举类|
|avatar|string|false|none||用户头像|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举类|
|loginIp|string|true|none||最后登录 IP|
|loginDate|string(date-time)|true|none||最后登录时间|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultListUserSimpleRespVO">CommonResultListUserSimpleRespVO</h2>

<a id="schemacommonresultlistusersimplerespvo"></a>
<a id="schema_CommonResultListUserSimpleRespVO"></a>
<a id="tocScommonresultlistusersimplerespvo"></a>
<a id="tocscommonresultlistusersimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "nickname": "芋道",
      "deptId": "我是一个用户",
      "deptName": "IT 部"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[UserSimpleRespVO](#schemausersimplerespvo)]|false|none||[管理后台 - 用户精简信息 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_UserSimpleRespVO">UserSimpleRespVO</h2>

<a id="schemausersimplerespvo"></a>
<a id="schema_UserSimpleRespVO"></a>
<a id="tocSusersimplerespvo"></a>
<a id="tocsusersimplerespvo"></a>

```json
{
  "id": 1024,
  "nickname": "芋道",
  "deptId": "我是一个用户",
  "deptName": "IT 部"
}

```

管理后台 - 用户精简信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||用户编号|
|nickname|string|true|none||用户昵称|
|deptId|integer(int64)|false|none||部门ID|
|deptName|string|false|none||部门名称|

<h2 id="tocS_CommonResultUserRespVO">CommonResultUserRespVO</h2>

<a id="schemacommonresultuserrespvo"></a>
<a id="schema_CommonResultUserRespVO"></a>
<a id="tocScommonresultuserrespvo"></a>
<a id="tocscommonresultuserrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "username": "yudao",
    "nickname": "芋艿",
    "remark": "我是一个用户",
    "deptId": "我是一个用户",
    "deptName": "IT 部",
    "postIds": 1,
    "email": "<EMAIL>",
    "mobile": ***********,
    "sex": 1,
    "avatar": "https://www.iocoder.cn/xxx.png",
    "status": 1,
    "loginIp": "***********",
    "loginDate": "时间戳格式",
    "createTime": "时间戳格式"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[UserRespVO](#schemauserrespvo)|false|none||管理后台 - 用户信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListTenantRespVO">CommonResultListTenantRespVO</h2>

<a id="schemacommonresultlisttenantrespvo"></a>
<a id="schema_CommonResultListTenantRespVO"></a>
<a id="tocScommonresultlisttenantrespvo"></a>
<a id="tocscommonresultlisttenantrespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "芋道",
      "contactName": "芋艿",
      "contactMobile": ***********,
      "status": 1,
      "website": "https://www.iocoder.cn",
      "packageId": 1024,
      "expireTime": "2019-08-24T14:15:22Z",
      "accountCount": 1024,
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[TenantRespVO](#schematenantrespvo)]|false|none||[管理后台 - 租户 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_TenantRespVO">TenantRespVO</h2>

<a id="schematenantrespvo"></a>
<a id="schema_TenantRespVO"></a>
<a id="tocStenantrespvo"></a>
<a id="tocstenantrespvo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "contactName": "芋艿",
  "contactMobile": ***********,
  "status": 1,
  "website": "https://www.iocoder.cn",
  "packageId": 1024,
  "expireTime": "2019-08-24T14:15:22Z",
  "accountCount": 1024,
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 租户 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||租户编号|
|name|string|true|none||租户名|
|contactName|string|true|none||联系人|
|contactMobile|string|false|none||联系手机|
|status|integer(int32)|true|none||租户状态|
|website|string|false|none||绑定域名|
|packageId|integer(int64)|true|none||租户套餐编号|
|expireTime|string(date-time)|true|none||过期时间|
|accountCount|integer(int32)|true|none||账号数量|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultPageResultTenantRespVO">CommonResultPageResultTenantRespVO</h2>

<a id="schemacommonresultpageresulttenantrespvo"></a>
<a id="schema_CommonResultPageResultTenantRespVO"></a>
<a id="tocScommonresultpageresulttenantrespvo"></a>
<a id="tocscommonresultpageresulttenantrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "name": "芋道",
        "contactName": "芋艿",
        "contactMobile": ***********,
        "status": 1,
        "website": "https://www.iocoder.cn",
        "packageId": 1024,
        "expireTime": "2019-08-24T14:15:22Z",
        "accountCount": 1024,
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultTenantRespVO](#schemapageresulttenantrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultTenantRespVO">PageResultTenantRespVO</h2>

<a id="schemapageresulttenantrespvo"></a>
<a id="schema_PageResultTenantRespVO"></a>
<a id="tocSpageresulttenantrespvo"></a>
<a id="tocspageresulttenantrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "name": "芋道",
      "contactName": "芋艿",
      "contactMobile": ***********,
      "status": 1,
      "website": "https://www.iocoder.cn",
      "packageId": 1024,
      "expireTime": "2019-08-24T14:15:22Z",
      "accountCount": 1024,
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[TenantRespVO](#schematenantrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultTenantRespVO">CommonResultTenantRespVO</h2>

<a id="schemacommonresulttenantrespvo"></a>
<a id="schema_CommonResultTenantRespVO"></a>
<a id="tocScommonresulttenantrespvo"></a>
<a id="tocscommonresulttenantrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "芋道",
    "contactName": "芋艿",
    "contactMobile": ***********,
    "status": 1,
    "website": "https://www.iocoder.cn",
    "packageId": 1024,
    "expireTime": "2019-08-24T14:15:22Z",
    "accountCount": 1024,
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[TenantRespVO](#schematenantrespvo)|false|none||管理后台 - 租户 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultTenantPackageRespVO">CommonResultPageResultTenantPackageRespVO</h2>

<a id="schemacommonresultpageresulttenantpackagerespvo"></a>
<a id="schema_CommonResultPageResultTenantPackageRespVO"></a>
<a id="tocScommonresultpageresulttenantpackagerespvo"></a>
<a id="tocscommonresultpageresulttenantpackagerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "name": "VIP",
        "status": 1,
        "remark": "好",
        "menuIds": [
          0
        ],
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultTenantPackageRespVO](#schemapageresulttenantpackagerespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultTenantPackageRespVO">PageResultTenantPackageRespVO</h2>

<a id="schemapageresulttenantpackagerespvo"></a>
<a id="schema_PageResultTenantPackageRespVO"></a>
<a id="tocSpageresulttenantpackagerespvo"></a>
<a id="tocspageresulttenantpackagerespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "name": "VIP",
      "status": 1,
      "remark": "好",
      "menuIds": [
        0
      ],
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[TenantPackageRespVO](#schematenantpackagerespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_TenantPackageRespVO">TenantPackageRespVO</h2>

<a id="schematenantpackagerespvo"></a>
<a id="schema_TenantPackageRespVO"></a>
<a id="tocStenantpackagerespvo"></a>
<a id="tocstenantpackagerespvo"></a>

```json
{
  "id": 1024,
  "name": "VIP",
  "status": 1,
  "remark": "好",
  "menuIds": [
    0
  ],
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 租户套餐 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||套餐编号|
|name|string|true|none||套餐名|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举|
|remark|string|false|none||备注|
|menuIds|[integer]|true|none||关联的菜单编号|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultTenantPackageRespVO">CommonResultTenantPackageRespVO</h2>

<a id="schemacommonresulttenantpackagerespvo"></a>
<a id="schema_CommonResultTenantPackageRespVO"></a>
<a id="tocScommonresulttenantpackagerespvo"></a>
<a id="tocscommonresulttenantpackagerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "VIP",
    "status": 1,
    "remark": "好",
    "menuIds": [
      0
    ],
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[TenantPackageRespVO](#schematenantpackagerespvo)|false|none||管理后台 - 租户套餐 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListTenantPackageSimpleRespVO">CommonResultListTenantPackageSimpleRespVO</h2>

<a id="schemacommonresultlisttenantpackagesimplerespvo"></a>
<a id="schema_CommonResultListTenantPackageSimpleRespVO"></a>
<a id="tocScommonresultlisttenantpackagesimplerespvo"></a>
<a id="tocscommonresultlisttenantpackagesimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "VIP"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[TenantPackageSimpleRespVO](#schematenantpackagesimplerespvo)]|false|none||[管理后台 - 租户套餐精简 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_TenantPackageSimpleRespVO">TenantPackageSimpleRespVO</h2>

<a id="schematenantpackagesimplerespvo"></a>
<a id="schema_TenantPackageSimpleRespVO"></a>
<a id="tocStenantpackagesimplerespvo"></a>
<a id="tocstenantpackagesimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "VIP"
}

```

管理后台 - 租户套餐精简 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||套餐编号|
|name|string|true|none||套餐名|

<h2 id="tocS_CommonResultPageResultSocialUserRespVO">CommonResultPageResultSocialUserRespVO</h2>

<a id="schemacommonresultpageresultsocialuserrespvo"></a>
<a id="schema_CommonResultPageResultSocialUserRespVO"></a>
<a id="tocScommonresultpageresultsocialuserrespvo"></a>
<a id="tocscommonresultpageresultsocialuserrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 14569,
        "type": 30,
        "openid": 666,
        "token": 666,
        "rawTokenInfo": {},
        "nickname": "芋艿",
        "avatar": "https://www.iocoder.cn/xxx.png",
        "rawUserInfo": {},
        "code": 666666,
        "state": 123456,
        "createTime": "2019-08-24T14:15:22Z",
        "updateTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultSocialUserRespVO](#schemapageresultsocialuserrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultSocialUserRespVO">PageResultSocialUserRespVO</h2>

<a id="schemapageresultsocialuserrespvo"></a>
<a id="schema_PageResultSocialUserRespVO"></a>
<a id="tocSpageresultsocialuserrespvo"></a>
<a id="tocspageresultsocialuserrespvo"></a>

```json
{
  "list": [
    {
      "id": 14569,
      "type": 30,
      "openid": 666,
      "token": 666,
      "rawTokenInfo": {},
      "nickname": "芋艿",
      "avatar": "https://www.iocoder.cn/xxx.png",
      "rawUserInfo": {},
      "code": 666666,
      "state": 123456,
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[SocialUserRespVO](#schemasocialuserrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_SocialUserRespVO">SocialUserRespVO</h2>

<a id="schemasocialuserrespvo"></a>
<a id="schema_SocialUserRespVO"></a>
<a id="tocSsocialuserrespvo"></a>
<a id="tocssocialuserrespvo"></a>

```json
{
  "id": 14569,
  "type": 30,
  "openid": 666,
  "token": 666,
  "rawTokenInfo": {},
  "nickname": "芋艿",
  "avatar": "https://www.iocoder.cn/xxx.png",
  "rawUserInfo": {},
  "code": 666666,
  "state": 123456,
  "createTime": "2019-08-24T14:15:22Z",
  "updateTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 社交用户 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||主键(自增策略)|
|type|integer(int32)|true|none||社交平台的类型|
|openid|string|true|none||社交 openid|
|token|string|true|none||社交 token|
|rawTokenInfo|string|true|none||原始 Token 数据，一般是 JSON 格式|
|nickname|string|true|none||用户昵称|
|avatar|string|false|none||用户头像|
|rawUserInfo|string|true|none||原始用户数据，一般是 JSON 格式|
|code|string|true|none||最后一次的认证 code|
|state|string|true|none||最后一次的认证 state|
|createTime|string(date-time)|true|none||创建时间|
|updateTime|string(date-time)|true|none||更新时间|

<h2 id="tocS_CommonResultSocialUserRespVO">CommonResultSocialUserRespVO</h2>

<a id="schemacommonresultsocialuserrespvo"></a>
<a id="schema_CommonResultSocialUserRespVO"></a>
<a id="tocScommonresultsocialuserrespvo"></a>
<a id="tocscommonresultsocialuserrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 14569,
    "type": 30,
    "openid": 666,
    "token": 666,
    "rawTokenInfo": {},
    "nickname": "芋艿",
    "avatar": "https://www.iocoder.cn/xxx.png",
    "rawUserInfo": {},
    "code": 666666,
    "state": 123456,
    "createTime": "2019-08-24T14:15:22Z",
    "updateTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[SocialUserRespVO](#schemasocialuserrespvo)|false|none||管理后台 - 社交用户 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListSocialUserRespVO">CommonResultListSocialUserRespVO</h2>

<a id="schemacommonresultlistsocialuserrespvo"></a>
<a id="schema_CommonResultListSocialUserRespVO"></a>
<a id="tocScommonresultlistsocialuserrespvo"></a>
<a id="tocscommonresultlistsocialuserrespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 14569,
      "type": 30,
      "openid": 666,
      "token": 666,
      "rawTokenInfo": {},
      "nickname": "芋艿",
      "avatar": "https://www.iocoder.cn/xxx.png",
      "rawUserInfo": {},
      "code": 666666,
      "state": 123456,
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[SocialUserRespVO](#schemasocialuserrespvo)]|false|none||[管理后台 - 社交用户 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultSocialClientRespVO">CommonResultPageResultSocialClientRespVO</h2>

<a id="schemacommonresultpageresultsocialclientrespvo"></a>
<a id="schema_CommonResultPageResultSocialClientRespVO"></a>
<a id="tocScommonresultpageresultsocialclientrespvo"></a>
<a id="tocscommonresultpageresultsocialclientrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 27162,
        "name": "yudao商城",
        "socialType": 31,
        "userType": 2,
        "clientId": "wwd411c69a39ad2e54",
        "clientSecret": "peter",
        "agentId": 2000045,
        "status": 1,
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultSocialClientRespVO](#schemapageresultsocialclientrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultSocialClientRespVO">PageResultSocialClientRespVO</h2>

<a id="schemapageresultsocialclientrespvo"></a>
<a id="schema_PageResultSocialClientRespVO"></a>
<a id="tocSpageresultsocialclientrespvo"></a>
<a id="tocspageresultsocialclientrespvo"></a>

```json
{
  "list": [
    {
      "id": 27162,
      "name": "yudao商城",
      "socialType": 31,
      "userType": 2,
      "clientId": "wwd411c69a39ad2e54",
      "clientSecret": "peter",
      "agentId": 2000045,
      "status": 1,
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[SocialClientRespVO](#schemasocialclientrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_SocialClientRespVO">SocialClientRespVO</h2>

<a id="schemasocialclientrespvo"></a>
<a id="schema_SocialClientRespVO"></a>
<a id="tocSsocialclientrespvo"></a>
<a id="tocssocialclientrespvo"></a>

```json
{
  "id": 27162,
  "name": "yudao商城",
  "socialType": 31,
  "userType": 2,
  "clientId": "wwd411c69a39ad2e54",
  "clientSecret": "peter",
  "agentId": 2000045,
  "status": 1,
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 社交客户端 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||应用名|
|socialType|integer(int32)|true|none||社交平台的类型|
|userType|integer(int32)|true|none||用户类型|
|clientId|string|true|none||客户端编号|
|clientSecret|string|true|none||客户端密钥|
|agentId|string|true|none||授权方的网页应用编号|
|status|integer(int32)|true|none||状态|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultSocialClientRespVO">CommonResultSocialClientRespVO</h2>

<a id="schemacommonresultsocialclientrespvo"></a>
<a id="schema_CommonResultSocialClientRespVO"></a>
<a id="tocScommonresultsocialclientrespvo"></a>
<a id="tocscommonresultsocialclientrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 27162,
    "name": "yudao商城",
    "socialType": 31,
    "userType": 2,
    "clientId": "wwd411c69a39ad2e54",
    "clientSecret": "peter",
    "agentId": 2000045,
    "status": 1,
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[SocialClientRespVO](#schemasocialclientrespvo)|false|none||管理后台 - 社交客户端 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultSmsTemplateRespVO">CommonResultPageResultSmsTemplateRespVO</h2>

<a id="schemacommonresultpageresultsmstemplaterespvo"></a>
<a id="schema_CommonResultPageResultSmsTemplateRespVO"></a>
<a id="tocScommonresultpageresultsmstemplaterespvo"></a>
<a id="tocscommonresultpageresultsmstemplaterespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "type": 1,
        "status": 1,
        "code": "test_01",
        "name": "yudao",
        "content": "你好，{name}。你长的太{like}啦！",
        "params": "name,code",
        "remark": "哈哈哈",
        "apiTemplateId": 4383920,
        "channelId": 10,
        "channelCode": "ALIYUN",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultSmsTemplateRespVO](#schemapageresultsmstemplaterespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultSmsTemplateRespVO">PageResultSmsTemplateRespVO</h2>

<a id="schemapageresultsmstemplaterespvo"></a>
<a id="schema_PageResultSmsTemplateRespVO"></a>
<a id="tocSpageresultsmstemplaterespvo"></a>
<a id="tocspageresultsmstemplaterespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "type": 1,
      "status": 1,
      "code": "test_01",
      "name": "yudao",
      "content": "你好，{name}。你长的太{like}啦！",
      "params": "name,code",
      "remark": "哈哈哈",
      "apiTemplateId": 4383920,
      "channelId": 10,
      "channelCode": "ALIYUN",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[SmsTemplateRespVO](#schemasmstemplaterespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_SmsTemplateRespVO">SmsTemplateRespVO</h2>

<a id="schemasmstemplaterespvo"></a>
<a id="schema_SmsTemplateRespVO"></a>
<a id="tocSsmstemplaterespvo"></a>
<a id="tocssmstemplaterespvo"></a>

```json
{
  "id": 1024,
  "type": 1,
  "status": 1,
  "code": "test_01",
  "name": "yudao",
  "content": "你好，{name}。你长的太{like}啦！",
  "params": "name,code",
  "remark": "哈哈哈",
  "apiTemplateId": 4383920,
  "channelId": 10,
  "channelCode": "ALIYUN",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 短信模板 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|type|integer(int32)|true|none||短信类型，参见 SmsTemplateTypeEnum 枚举类|
|status|integer(int32)|true|none||开启状态，参见 CommonStatusEnum 枚举类|
|code|string|true|none||模板编码|
|name|string|true|none||模板名称|
|content|string|true|none||模板内容|
|params|[string]|false|none||参数数组|
|remark|string|false|none||备注|
|apiTemplateId|string|true|none||短信 API 的模板编号|
|channelId|integer(int64)|true|none||短信渠道编号|
|channelCode|string|true|none||短信渠道编码|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultSmsTemplateRespVO">CommonResultSmsTemplateRespVO</h2>

<a id="schemacommonresultsmstemplaterespvo"></a>
<a id="schema_CommonResultSmsTemplateRespVO"></a>
<a id="tocScommonresultsmstemplaterespvo"></a>
<a id="tocscommonresultsmstemplaterespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "type": 1,
    "status": 1,
    "code": "test_01",
    "name": "yudao",
    "content": "你好，{name}。你长的太{like}啦！",
    "params": "name,code",
    "remark": "哈哈哈",
    "apiTemplateId": 4383920,
    "channelId": 10,
    "channelCode": "ALIYUN",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[SmsTemplateRespVO](#schemasmstemplaterespvo)|false|none||管理后台 - 短信模板 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultSmsLogRespVO">CommonResultPageResultSmsLogRespVO</h2>

<a id="schemacommonresultpageresultsmslogrespvo"></a>
<a id="schema_CommonResultPageResultSmsLogRespVO"></a>
<a id="tocScommonresultpageresultsmslogrespvo"></a>
<a id="tocscommonresultpageresultsmslogrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "channelId": 10,
        "channelCode": "ALIYUN",
        "templateId": 20,
        "templateCode": "test-01",
        "templateType": 1,
        "templateContent": "你好，你的验证码是 1024",
        "templateParams": "name,code",
        "apiTemplateId": "SMS_207945135",
        "mobile": ***********,
        "userId": 10,
        "userType": 1,
        "sendStatus": 1,
        "sendTime": "2019-08-24T14:15:22Z",
        "apiSendCode": "SUCCESS",
        "apiSendMsg": "成功",
        "apiRequestId": "3837C6D3-B96F-428C-BBB2-86135D4B5B99",
        "apiSerialNo": 62923244790,
        "receiveStatus": 0,
        "receiveTime": "2019-08-24T14:15:22Z",
        "apiReceiveCode": "DELIVRD",
        "apiReceiveMsg": "用户接收成功",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultSmsLogRespVO](#schemapageresultsmslogrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultSmsLogRespVO">PageResultSmsLogRespVO</h2>

<a id="schemapageresultsmslogrespvo"></a>
<a id="schema_PageResultSmsLogRespVO"></a>
<a id="tocSpageresultsmslogrespvo"></a>
<a id="tocspageresultsmslogrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "channelId": 10,
      "channelCode": "ALIYUN",
      "templateId": 20,
      "templateCode": "test-01",
      "templateType": 1,
      "templateContent": "你好，你的验证码是 1024",
      "templateParams": "name,code",
      "apiTemplateId": "SMS_207945135",
      "mobile": ***********,
      "userId": 10,
      "userType": 1,
      "sendStatus": 1,
      "sendTime": "2019-08-24T14:15:22Z",
      "apiSendCode": "SUCCESS",
      "apiSendMsg": "成功",
      "apiRequestId": "3837C6D3-B96F-428C-BBB2-86135D4B5B99",
      "apiSerialNo": 62923244790,
      "receiveStatus": 0,
      "receiveTime": "2019-08-24T14:15:22Z",
      "apiReceiveCode": "DELIVRD",
      "apiReceiveMsg": "用户接收成功",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[SmsLogRespVO](#schemasmslogrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_SmsLogRespVO">SmsLogRespVO</h2>

<a id="schemasmslogrespvo"></a>
<a id="schema_SmsLogRespVO"></a>
<a id="tocSsmslogrespvo"></a>
<a id="tocssmslogrespvo"></a>

```json
{
  "id": 1024,
  "channelId": 10,
  "channelCode": "ALIYUN",
  "templateId": 20,
  "templateCode": "test-01",
  "templateType": 1,
  "templateContent": "你好，你的验证码是 1024",
  "templateParams": "name,code",
  "apiTemplateId": "SMS_207945135",
  "mobile": ***********,
  "userId": 10,
  "userType": 1,
  "sendStatus": 1,
  "sendTime": "2019-08-24T14:15:22Z",
  "apiSendCode": "SUCCESS",
  "apiSendMsg": "成功",
  "apiRequestId": "3837C6D3-B96F-428C-BBB2-86135D4B5B99",
  "apiSerialNo": 62923244790,
  "receiveStatus": 0,
  "receiveTime": "2019-08-24T14:15:22Z",
  "apiReceiveCode": "DELIVRD",
  "apiReceiveMsg": "用户接收成功",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 短信日志 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|channelId|integer(int64)|true|none||短信渠道编号|
|channelCode|string|true|none||短信渠道编码|
|templateId|integer(int64)|true|none||模板编号|
|templateCode|string|true|none||模板编码|
|templateType|integer(int32)|true|none||短信类型|
|templateContent|string|true|none||短信内容|
|templateParams|object|true|none||短信参数|
|» **additionalProperties**|object|false|none||none|
|apiTemplateId|string|true|none||短信 API 的模板编号|
|mobile|string|true|none||手机号|
|userId|integer(int64)|false|none||用户编号|
|userType|integer(int32)|false|none||用户类型|
|sendStatus|integer(int32)|true|none||发送状态|
|sendTime|string(date-time)|false|none||发送时间|
|apiSendCode|string|false|none||短信 API 发送结果的编码|
|apiSendMsg|string|false|none||短信 API 发送失败的提示|
|apiRequestId|string|false|none||短信 API 发送返回的唯一请求 ID|
|apiSerialNo|string|false|none||短信 API 发送返回的序号|
|receiveStatus|integer(int32)|true|none||接收状态|
|receiveTime|string(date-time)|false|none||接收时间|
|apiReceiveCode|string|false|none||API 接收结果的编码|
|apiReceiveMsg|string|false|none||API 接收结果的说明|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultPageResultSmsChannelRespVO">CommonResultPageResultSmsChannelRespVO</h2>

<a id="schemacommonresultpageresultsmschannelrespvo"></a>
<a id="schema_CommonResultPageResultSmsChannelRespVO"></a>
<a id="tocScommonresultpageresultsmschannelrespvo"></a>
<a id="tocscommonresultpageresultsmschannelrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "signature": "芋道源码",
        "code": "YUN_PIAN",
        "status": 1,
        "remark": "好吃！",
        "apiKey": "yudao",
        "apiSecret": "yuanma",
        "callbackUrl": "https://www.iocoder.cn",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultSmsChannelRespVO](#schemapageresultsmschannelrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultSmsChannelRespVO">PageResultSmsChannelRespVO</h2>

<a id="schemapageresultsmschannelrespvo"></a>
<a id="schema_PageResultSmsChannelRespVO"></a>
<a id="tocSpageresultsmschannelrespvo"></a>
<a id="tocspageresultsmschannelrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "signature": "芋道源码",
      "code": "YUN_PIAN",
      "status": 1,
      "remark": "好吃！",
      "apiKey": "yudao",
      "apiSecret": "yuanma",
      "callbackUrl": "https://www.iocoder.cn",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[SmsChannelRespVO](#schemasmschannelrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_SmsChannelRespVO">SmsChannelRespVO</h2>

<a id="schemasmschannelrespvo"></a>
<a id="schema_SmsChannelRespVO"></a>
<a id="tocSsmschannelrespvo"></a>
<a id="tocssmschannelrespvo"></a>

```json
{
  "id": 1024,
  "signature": "芋道源码",
  "code": "YUN_PIAN",
  "status": 1,
  "remark": "好吃！",
  "apiKey": "yudao",
  "apiSecret": "yuanma",
  "callbackUrl": "https://www.iocoder.cn",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 短信渠道 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|signature|string|true|none||短信签名|
|code|string|true|none||渠道编码，参见 SmsChannelEnum 枚举类|
|status|integer(int32)|true|none||启用状态|
|remark|string|false|none||备注|
|apiKey|string|true|none||短信 API 的账号|
|apiSecret|string|false|none||短信 API 的密钥|
|callbackUrl|string|false|none||短信发送回调 URL|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultListSmsChannelSimpleRespVO">CommonResultListSmsChannelSimpleRespVO</h2>

<a id="schemacommonresultlistsmschannelsimplerespvo"></a>
<a id="schema_CommonResultListSmsChannelSimpleRespVO"></a>
<a id="tocScommonresultlistsmschannelsimplerespvo"></a>
<a id="tocscommonresultlistsmschannelsimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "signature": "芋道源码",
      "code": "YUN_PIAN"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[SmsChannelSimpleRespVO](#schemasmschannelsimplerespvo)]|false|none||[管理后台 - 短信渠道精简 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_SmsChannelSimpleRespVO">SmsChannelSimpleRespVO</h2>

<a id="schemasmschannelsimplerespvo"></a>
<a id="schema_SmsChannelSimpleRespVO"></a>
<a id="tocSsmschannelsimplerespvo"></a>
<a id="tocssmschannelsimplerespvo"></a>

```json
{
  "id": 1024,
  "signature": "芋道源码",
  "code": "YUN_PIAN"
}

```

管理后台 - 短信渠道精简 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|signature|string|true|none||短信签名|
|code|string|true|none||渠道编码，参见 SmsChannelEnum 枚举类|

<h2 id="tocS_CommonResultSmsChannelRespVO">CommonResultSmsChannelRespVO</h2>

<a id="schemacommonresultsmschannelrespvo"></a>
<a id="schema_CommonResultSmsChannelRespVO"></a>
<a id="tocScommonresultsmschannelrespvo"></a>
<a id="tocscommonresultsmschannelrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "signature": "芋道源码",
    "code": "YUN_PIAN",
    "status": 1,
    "remark": "好吃！",
    "apiKey": "yudao",
    "apiSecret": "yuanma",
    "callbackUrl": "https://www.iocoder.cn",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[SmsChannelRespVO](#schemasmschannelrespvo)|false|none||管理后台 - 短信渠道 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultRoleRespVO">CommonResultPageResultRoleRespVO</h2>

<a id="schemacommonresultpageresultrolerespvo"></a>
<a id="schema_CommonResultPageResultRoleRespVO"></a>
<a id="tocScommonresultpageresultrolerespvo"></a>
<a id="tocscommonresultpageresultrolerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "管理员",
        "code": "admin",
        "sort": 1024,
        "status": 1,
        "type": 1,
        "remark": "我是一个角色",
        "dataScope": 1,
        "dataScopeDeptIds": 1,
        "createTime": "时间戳格式"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultRoleRespVO](#schemapageresultrolerespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultRoleRespVO">PageResultRoleRespVO</h2>

<a id="schemapageresultrolerespvo"></a>
<a id="schema_PageResultRoleRespVO"></a>
<a id="tocSpageresultrolerespvo"></a>
<a id="tocspageresultrolerespvo"></a>

```json
{
  "list": [
    {
      "id": 1,
      "name": "管理员",
      "code": "admin",
      "sort": 1024,
      "status": 1,
      "type": 1,
      "remark": "我是一个角色",
      "dataScope": 1,
      "dataScopeDeptIds": 1,
      "createTime": "时间戳格式"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[RoleRespVO](#schemarolerespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_RoleRespVO">RoleRespVO</h2>

<a id="schemarolerespvo"></a>
<a id="schema_RoleRespVO"></a>
<a id="tocSrolerespvo"></a>
<a id="tocsrolerespvo"></a>

```json
{
  "id": 1,
  "name": "管理员",
  "code": "admin",
  "sort": 1024,
  "status": 1,
  "type": 1,
  "remark": "我是一个角色",
  "dataScope": 1,
  "dataScopeDeptIds": 1,
  "createTime": "时间戳格式"
}

```

管理后台 - 角色信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||角色编号|
|name|string|true|none||角色名称|
|code|string|true|none||角色标志|
|sort|integer(int32)|true|none||显示顺序|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举类|
|type|integer(int32)|true|none||角色类型，参见 RoleTypeEnum 枚举类|
|remark|string|false|none||备注|
|dataScope|integer(int32)|true|none||数据范围，参见 DataScopeEnum 枚举类|
|dataScopeDeptIds|[integer]|false|none||数据范围(指定部门数组)|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultListRoleRespVO">CommonResultListRoleRespVO</h2>

<a id="schemacommonresultlistrolerespvo"></a>
<a id="schema_CommonResultListRoleRespVO"></a>
<a id="tocScommonresultlistrolerespvo"></a>
<a id="tocscommonresultlistrolerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "管理员",
      "code": "admin",
      "sort": 1024,
      "status": 1,
      "type": 1,
      "remark": "我是一个角色",
      "dataScope": 1,
      "dataScopeDeptIds": 1,
      "createTime": "时间戳格式"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[RoleRespVO](#schemarolerespvo)]|false|none||[管理后台 - 角色信息 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultRoleRespVO">CommonResultRoleRespVO</h2>

<a id="schemacommonresultrolerespvo"></a>
<a id="schema_CommonResultRoleRespVO"></a>
<a id="tocScommonresultrolerespvo"></a>
<a id="tocscommonresultrolerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "name": "管理员",
    "code": "admin",
    "sort": 1024,
    "status": 1,
    "type": 1,
    "remark": "我是一个角色",
    "dataScope": 1,
    "dataScopeDeptIds": 1,
    "createTime": "时间戳格式"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[RoleRespVO](#schemarolerespvo)|false|none||管理后台 - 角色信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultPostRespVO">CommonResultPageResultPostRespVO</h2>

<a id="schemacommonresultpageresultpostrespvo"></a>
<a id="schema_CommonResultPageResultPostRespVO"></a>
<a id="tocScommonresultpageresultpostrespvo"></a>
<a id="tocscommonresultpageresultpostrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "name": "小土豆",
        "code": "yudao",
        "sort": 1024,
        "status": 1,
        "remark": "快乐的备注",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultPostRespVO](#schemapageresultpostrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultPostRespVO">PageResultPostRespVO</h2>

<a id="schemapageresultpostrespvo"></a>
<a id="schema_PageResultPostRespVO"></a>
<a id="tocSpageresultpostrespvo"></a>
<a id="tocspageresultpostrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "name": "小土豆",
      "code": "yudao",
      "sort": 1024,
      "status": 1,
      "remark": "快乐的备注",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[PostRespVO](#schemapostrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_PostRespVO">PostRespVO</h2>

<a id="schemapostrespvo"></a>
<a id="schema_PostRespVO"></a>
<a id="tocSpostrespvo"></a>
<a id="tocspostrespvo"></a>

```json
{
  "id": 1024,
  "name": "小土豆",
  "code": "yudao",
  "sort": 1024,
  "status": 1,
  "remark": "快乐的备注",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 岗位信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||岗位序号|
|name|string|true|none||岗位名称|
|code|string|true|none||岗位编码|
|sort|integer(int32)|true|none||显示顺序|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举类|
|remark|string|false|none||备注|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultListPostSimpleRespVO">CommonResultListPostSimpleRespVO</h2>

<a id="schemacommonresultlistpostsimplerespvo"></a>
<a id="schema_CommonResultListPostSimpleRespVO"></a>
<a id="tocScommonresultlistpostsimplerespvo"></a>
<a id="tocscommonresultlistpostsimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "小土豆"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[PostSimpleRespVO](#schemapostsimplerespvo)]|false|none||[管理后台 - 岗位信息的精简 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPostRespVO">CommonResultPostRespVO</h2>

<a id="schemacommonresultpostrespvo"></a>
<a id="schema_CommonResultPostRespVO"></a>
<a id="tocScommonresultpostrespvo"></a>
<a id="tocscommonresultpostrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "小土豆",
    "code": "yudao",
    "sort": 1024,
    "status": 1,
    "remark": "快乐的备注",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PostRespVO](#schemapostrespvo)|false|none||管理后台 - 岗位信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultSetLong">CommonResultSetLong</h2>

<a id="schemacommonresultsetlong"></a>
<a id="schema_CommonResultSetLong"></a>
<a id="tocScommonresultsetlong"></a>
<a id="tocscommonresultsetlong"></a>

```json
{
  "code": 0,
  "data": [
    0
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[integer]|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultOperateLogRespVO">CommonResultPageResultOperateLogRespVO</h2>

<a id="schemacommonresultpageresultoperatelogrespvo"></a>
<a id="schema_CommonResultPageResultOperateLogRespVO"></a>
<a id="tocScommonresultpageresultoperatelogrespvo"></a>
<a id="tocscommonresultpageresultoperatelogrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "traceId": "89aca178-a370-411c-ae02-3f0d672be4ab",
        "userId": 1024,
        "userName": "芋艿",
        "type": "订单",
        "subType": "创建订单",
        "bizId": 1,
        "action": "修改编号为 1 的用户信息，将性别从男改成女，将姓名从芋道改成源码。",
        "extra": "{'orderId': 1}",
        "requestMethod": "GET",
        "requestUrl": "/xxx/yyy",
        "userIp": "127.0.0.1",
        "userAgent": "Mozilla/5.0",
        "createTime": "2019-08-24T14:15:22Z",
        "transMap": {
          "property1": {},
          "property2": {}
        }
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultOperateLogRespVO](#schemapageresultoperatelogrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_OperateLogRespVO">OperateLogRespVO</h2>

<a id="schemaoperatelogrespvo"></a>
<a id="schema_OperateLogRespVO"></a>
<a id="tocSoperatelogrespvo"></a>
<a id="tocsoperatelogrespvo"></a>

```json
{
  "id": 1024,
  "traceId": "89aca178-a370-411c-ae02-3f0d672be4ab",
  "userId": 1024,
  "userName": "芋艿",
  "type": "订单",
  "subType": "创建订单",
  "bizId": 1,
  "action": "修改编号为 1 的用户信息，将性别从男改成女，将姓名从芋道改成源码。",
  "extra": "{'orderId': 1}",
  "requestMethod": "GET",
  "requestUrl": "/xxx/yyy",
  "userIp": "127.0.0.1",
  "userAgent": "Mozilla/5.0",
  "createTime": "2019-08-24T14:15:22Z",
  "transMap": {
    "property1": {},
    "property2": {}
  }
}

```

管理后台 - 操作日志 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||日志编号|
|traceId|string|true|none||链路追踪编号|
|userId|integer(int64)|true|none||用户编号|
|userName|string|true|none||用户昵称|
|type|string|true|none||操作模块类型|
|subType|string|true|none||操作名|
|bizId|integer(int64)|true|none||操作模块业务编号|
|action|string|false|none||操作明细|
|extra|string|false|none||拓展字段|
|requestMethod|string|true|none||请求方法名|
|requestUrl|string|true|none||请求地址|
|userIp|string|true|none||用户 IP|
|userAgent|string|true|none||浏览器 UserAgent|
|createTime|string(date-time)|true|none||创建时间|
|transMap|object|false|none||none|
|» **additionalProperties**|object|false|none||none|

<h2 id="tocS_PageResultOperateLogRespVO">PageResultOperateLogRespVO</h2>

<a id="schemapageresultoperatelogrespvo"></a>
<a id="schema_PageResultOperateLogRespVO"></a>
<a id="tocSpageresultoperatelogrespvo"></a>
<a id="tocspageresultoperatelogrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "traceId": "89aca178-a370-411c-ae02-3f0d672be4ab",
      "userId": 1024,
      "userName": "芋艿",
      "type": "订单",
      "subType": "创建订单",
      "bizId": 1,
      "action": "修改编号为 1 的用户信息，将性别从男改成女，将姓名从芋道改成源码。",
      "extra": "{'orderId': 1}",
      "requestMethod": "GET",
      "requestUrl": "/xxx/yyy",
      "userIp": "127.0.0.1",
      "userAgent": "Mozilla/5.0",
      "createTime": "2019-08-24T14:15:22Z",
      "transMap": {
        "property1": {},
        "property2": {}
      }
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[OperateLogRespVO](#schemaoperatelogrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultOAuth2UserInfoRespVO">CommonResultOAuth2UserInfoRespVO</h2>

<a id="schemacommonresultoauth2userinforespvo"></a>
<a id="schema_CommonResultOAuth2UserInfoRespVO"></a>
<a id="tocScommonresultoauth2userinforespvo"></a>
<a id="tocscommonresultoauth2userinforespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "username": "芋艿",
    "nickname": "芋道",
    "email": "<EMAIL>",
    "mobile": ***********,
    "sex": 1,
    "avatar": "https://www.iocoder.cn/xxx.png",
    "dept": {
      "id": 1,
      "name": "研发部"
    },
    "posts": [
      {
        "id": 1,
        "name": "开发"
      }
    ]
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[OAuth2UserInfoRespVO](#schemaoauth2userinforespvo)|false|none||管理后台 - OAuth2 获得用户基本信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_Dept">Dept</h2>

<a id="schemadept"></a>
<a id="schema_Dept"></a>
<a id="tocSdept"></a>
<a id="tocsdept"></a>

```json
{
  "id": 1,
  "name": "研发部"
}

```

部门

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||部门编号|
|name|string|true|none||部门名称|

<h2 id="tocS_OAuth2UserInfoRespVO">OAuth2UserInfoRespVO</h2>

<a id="schemaoauth2userinforespvo"></a>
<a id="schema_OAuth2UserInfoRespVO"></a>
<a id="tocSoauth2userinforespvo"></a>
<a id="tocsoauth2userinforespvo"></a>

```json
{
  "id": 1,
  "username": "芋艿",
  "nickname": "芋道",
  "email": "<EMAIL>",
  "mobile": ***********,
  "sex": 1,
  "avatar": "https://www.iocoder.cn/xxx.png",
  "dept": {
    "id": 1,
    "name": "研发部"
  },
  "posts": [
    {
      "id": 1,
      "name": "开发"
    }
  ]
}

```

管理后台 - OAuth2 获得用户基本信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||用户编号|
|username|string|true|none||用户账号|
|nickname|string|true|none||用户昵称|
|email|string|false|none||用户邮箱|
|mobile|string|false|none||手机号码|
|sex|integer(int32)|false|none||用户性别，参见 SexEnum 枚举类|
|avatar|string|false|none||用户头像|
|dept|[Dept](#schemadept)|false|none||部门|
|posts|[[Post](#schemapost)]|false|none||[岗位]|

<h2 id="tocS_Post">Post</h2>

<a id="schemapost"></a>
<a id="schema_Post"></a>
<a id="tocSpost"></a>
<a id="tocspost"></a>

```json
{
  "id": 1,
  "name": "开发"
}

```

岗位

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||岗位编号|
|name|string|true|none||岗位名称|

<h2 id="tocS_Client">Client</h2>

<a id="schemaclient"></a>
<a id="schema_Client"></a>
<a id="tocSclient"></a>
<a id="tocsclient"></a>

```json
{
  "name": "土豆",
  "logo": "https://www.iocoder.cn/xx.png"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none||应用名|
|logo|string|true|none||应用图标|

<h2 id="tocS_CommonResultOAuth2OpenAuthorizeInfoRespVO">CommonResultOAuth2OpenAuthorizeInfoRespVO</h2>

<a id="schemacommonresultoauth2openauthorizeinforespvo"></a>
<a id="schema_CommonResultOAuth2OpenAuthorizeInfoRespVO"></a>
<a id="tocScommonresultoauth2openauthorizeinforespvo"></a>
<a id="tocscommonresultoauth2openauthorizeinforespvo"></a>

```json
{
  "code": 0,
  "data": {
    "client": {
      "name": "土豆",
      "logo": "https://www.iocoder.cn/xx.png"
    },
    "scopes": [
      {
        "key": "string",
        "value": true
      }
    ]
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[OAuth2OpenAuthorizeInfoRespVO](#schemaoauth2openauthorizeinforespvo)|false|none||管理后台 - 授权页的信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_KeyValueStringBoolean">KeyValueStringBoolean</h2>

<a id="schemakeyvaluestringboolean"></a>
<a id="schema_KeyValueStringBoolean"></a>
<a id="tocSkeyvaluestringboolean"></a>
<a id="tocskeyvaluestringboolean"></a>

```json
{
  "key": "string",
  "value": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|string|false|none||none|
|value|boolean|false|none||none|

<h2 id="tocS_OAuth2OpenAuthorizeInfoRespVO">OAuth2OpenAuthorizeInfoRespVO</h2>

<a id="schemaoauth2openauthorizeinforespvo"></a>
<a id="schema_OAuth2OpenAuthorizeInfoRespVO"></a>
<a id="tocSoauth2openauthorizeinforespvo"></a>
<a id="tocsoauth2openauthorizeinforespvo"></a>

```json
{
  "client": {
    "name": "土豆",
    "logo": "https://www.iocoder.cn/xx.png"
  },
  "scopes": [
    {
      "key": "string",
      "value": true
    }
  ]
}

```

管理后台 - 授权页的信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|client|[Client](#schemaclient)|false|none||none|
|scopes|[[KeyValueStringBoolean](#schemakeyvaluestringboolean)]|true|none||scope 的选中信息,使用 List 保证有序性，Key 是 scope，Value 为是否选中|

<h2 id="tocS_CommonResultPageResultOAuth2AccessTokenRespVO">CommonResultPageResultOAuth2AccessTokenRespVO</h2>

<a id="schemacommonresultpageresultoauth2accesstokenrespvo"></a>
<a id="schema_CommonResultPageResultOAuth2AccessTokenRespVO"></a>
<a id="tocScommonresultpageresultoauth2accesstokenrespvo"></a>
<a id="tocscommonresultpageresultoauth2accesstokenrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "accessToken": "tudou",
        "refreshToken": "nice",
        "userId": 666,
        "userType": 2,
        "clientId": 2,
        "createTime": "2019-08-24T14:15:22Z",
        "expiresTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultOAuth2AccessTokenRespVO](#schemapageresultoauth2accesstokenrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_OAuth2AccessTokenRespVO">OAuth2AccessTokenRespVO</h2>

<a id="schemaoauth2accesstokenrespvo"></a>
<a id="schema_OAuth2AccessTokenRespVO"></a>
<a id="tocSoauth2accesstokenrespvo"></a>
<a id="tocsoauth2accesstokenrespvo"></a>

```json
{
  "id": 1024,
  "accessToken": "tudou",
  "refreshToken": "nice",
  "userId": 666,
  "userType": 2,
  "clientId": 2,
  "createTime": "2019-08-24T14:15:22Z",
  "expiresTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 访问令牌 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|accessToken|string|true|none||访问令牌|
|refreshToken|string|true|none||刷新令牌|
|userId|integer(int64)|true|none||用户编号|
|userType|integer(int32)|true|none||用户类型，参见 UserTypeEnum 枚举|
|clientId|string|true|none||客户端编号|
|createTime|string(date-time)|true|none||创建时间|
|expiresTime|string(date-time)|true|none||过期时间|

<h2 id="tocS_PageResultOAuth2AccessTokenRespVO">PageResultOAuth2AccessTokenRespVO</h2>

<a id="schemapageresultoauth2accesstokenrespvo"></a>
<a id="schema_PageResultOAuth2AccessTokenRespVO"></a>
<a id="tocSpageresultoauth2accesstokenrespvo"></a>
<a id="tocspageresultoauth2accesstokenrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "accessToken": "tudou",
      "refreshToken": "nice",
      "userId": 666,
      "userType": 2,
      "clientId": 2,
      "createTime": "2019-08-24T14:15:22Z",
      "expiresTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[OAuth2AccessTokenRespVO](#schemaoauth2accesstokenrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultPageResultOAuth2ClientRespVO">CommonResultPageResultOAuth2ClientRespVO</h2>

<a id="schemacommonresultpageresultoauth2clientrespvo"></a>
<a id="schema_CommonResultPageResultOAuth2ClientRespVO"></a>
<a id="tocScommonresultpageresultoauth2clientrespvo"></a>
<a id="tocscommonresultpageresultoauth2clientrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "clientId": "tudou",
        "secret": "fan",
        "name": "土豆",
        "logo": "https://www.iocoder.cn/xx.png",
        "description": "我是一个应用",
        "status": 1,
        "accessTokenValiditySeconds": 8640,
        "refreshTokenValiditySeconds": 8640000,
        "redirectUris": "https://www.iocoder.cn",
        "authorizedGrantTypes": "password",
        "scopes": "user_info",
        "autoApproveScopes": "user_info",
        "authorities": "system:user:query",
        "resourceIds": 1024,
        "additionalInformation": "{yunai: true}",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultOAuth2ClientRespVO](#schemapageresultoauth2clientrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_OAuth2ClientRespVO">OAuth2ClientRespVO</h2>

<a id="schemaoauth2clientrespvo"></a>
<a id="schema_OAuth2ClientRespVO"></a>
<a id="tocSoauth2clientrespvo"></a>
<a id="tocsoauth2clientrespvo"></a>

```json
{
  "id": 1024,
  "clientId": "tudou",
  "secret": "fan",
  "name": "土豆",
  "logo": "https://www.iocoder.cn/xx.png",
  "description": "我是一个应用",
  "status": 1,
  "accessTokenValiditySeconds": 8640,
  "refreshTokenValiditySeconds": 8640000,
  "redirectUris": "https://www.iocoder.cn",
  "authorizedGrantTypes": "password",
  "scopes": "user_info",
  "autoApproveScopes": "user_info",
  "authorities": "system:user:query",
  "resourceIds": 1024,
  "additionalInformation": "{yunai: true}",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - OAuth2 客户端 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|clientId|string|true|none||客户端编号|
|secret|string|true|none||客户端密钥|
|name|string|true|none||应用名|
|logo|string|true|none||应用图标|
|description|string|false|none||应用描述|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举|
|accessTokenValiditySeconds|integer(int32)|true|none||访问令牌的有效期|
|refreshTokenValiditySeconds|integer(int32)|true|none||刷新令牌的有效期|
|redirectUris|[string]|true|none||可重定向的 URI 地址|
|authorizedGrantTypes|[string]|true|none||授权类型，参见 OAuth2GrantTypeEnum 枚举|
|scopes|[string]|false|none||授权范围|
|autoApproveScopes|[string]|false|none||自动通过的授权范围|
|authorities|[string]|false|none||权限|
|resourceIds|[string]|false|none||资源|
|additionalInformation|string|false|none||附加信息|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultOAuth2ClientRespVO">PageResultOAuth2ClientRespVO</h2>

<a id="schemapageresultoauth2clientrespvo"></a>
<a id="schema_PageResultOAuth2ClientRespVO"></a>
<a id="tocSpageresultoauth2clientrespvo"></a>
<a id="tocspageresultoauth2clientrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "clientId": "tudou",
      "secret": "fan",
      "name": "土豆",
      "logo": "https://www.iocoder.cn/xx.png",
      "description": "我是一个应用",
      "status": 1,
      "accessTokenValiditySeconds": 8640,
      "refreshTokenValiditySeconds": 8640000,
      "redirectUris": "https://www.iocoder.cn",
      "authorizedGrantTypes": "password",
      "scopes": "user_info",
      "autoApproveScopes": "user_info",
      "authorities": "system:user:query",
      "resourceIds": 1024,
      "additionalInformation": "{yunai: true}",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[OAuth2ClientRespVO](#schemaoauth2clientrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultOAuth2ClientRespVO">CommonResultOAuth2ClientRespVO</h2>

<a id="schemacommonresultoauth2clientrespvo"></a>
<a id="schema_CommonResultOAuth2ClientRespVO"></a>
<a id="tocScommonresultoauth2clientrespvo"></a>
<a id="tocscommonresultoauth2clientrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "clientId": "tudou",
    "secret": "fan",
    "name": "土豆",
    "logo": "https://www.iocoder.cn/xx.png",
    "description": "我是一个应用",
    "status": 1,
    "accessTokenValiditySeconds": 8640,
    "refreshTokenValiditySeconds": 8640000,
    "redirectUris": "https://www.iocoder.cn",
    "authorizedGrantTypes": "password",
    "scopes": "user_info",
    "autoApproveScopes": "user_info",
    "authorities": "system:user:query",
    "resourceIds": 1024,
    "additionalInformation": "{yunai: true}",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[OAuth2ClientRespVO](#schemaoauth2clientrespvo)|false|none||管理后台 - OAuth2 客户端 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultNotifyTemplateRespVO">CommonResultPageResultNotifyTemplateRespVO</h2>

<a id="schemacommonresultpageresultnotifytemplaterespvo"></a>
<a id="schema_CommonResultPageResultNotifyTemplateRespVO"></a>
<a id="tocScommonresultpageresultnotifytemplaterespvo"></a>
<a id="tocscommonresultpageresultnotifytemplaterespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "name": "测试模版",
        "code": "SEND_TEST",
        "type": 1,
        "nickname": "土豆",
        "content": "我是模版内容",
        "params": "name,code",
        "status": 1,
        "remark": "我是备注",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultNotifyTemplateRespVO](#schemapageresultnotifytemplaterespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_NotifyTemplateRespVO">NotifyTemplateRespVO</h2>

<a id="schemanotifytemplaterespvo"></a>
<a id="schema_NotifyTemplateRespVO"></a>
<a id="tocSnotifytemplaterespvo"></a>
<a id="tocsnotifytemplaterespvo"></a>

```json
{
  "id": 1024,
  "name": "测试模版",
  "code": "SEND_TEST",
  "type": 1,
  "nickname": "土豆",
  "content": "我是模版内容",
  "params": "name,code",
  "status": 1,
  "remark": "我是备注",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 站内信模版 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||ID|
|name|string|true|none||模版名称|
|code|string|true|none||模版编码|
|type|integer(int32)|true|none||模版类型，对应 system_notify_template_type 字典|
|nickname|string|true|none||发送人名称|
|content|string|true|none||模版内容|
|params|[string]|false|none||参数数组|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举|
|remark|string|false|none||备注|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultNotifyTemplateRespVO">PageResultNotifyTemplateRespVO</h2>

<a id="schemapageresultnotifytemplaterespvo"></a>
<a id="schema_PageResultNotifyTemplateRespVO"></a>
<a id="tocSpageresultnotifytemplaterespvo"></a>
<a id="tocspageresultnotifytemplaterespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "name": "测试模版",
      "code": "SEND_TEST",
      "type": 1,
      "nickname": "土豆",
      "content": "我是模版内容",
      "params": "name,code",
      "status": 1,
      "remark": "我是备注",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[NotifyTemplateRespVO](#schemanotifytemplaterespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultNotifyTemplateRespVO">CommonResultNotifyTemplateRespVO</h2>

<a id="schemacommonresultnotifytemplaterespvo"></a>
<a id="schema_CommonResultNotifyTemplateRespVO"></a>
<a id="tocScommonresultnotifytemplaterespvo"></a>
<a id="tocscommonresultnotifytemplaterespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "测试模版",
    "code": "SEND_TEST",
    "type": 1,
    "nickname": "土豆",
    "content": "我是模版内容",
    "params": "name,code",
    "status": 1,
    "remark": "我是备注",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[NotifyTemplateRespVO](#schemanotifytemplaterespvo)|false|none||管理后台 - 站内信模版 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultNotifyMessageRespVO">CommonResultPageResultNotifyMessageRespVO</h2>

<a id="schemacommonresultpageresultnotifymessagerespvo"></a>
<a id="schema_CommonResultPageResultNotifyMessageRespVO"></a>
<a id="tocScommonresultpageresultnotifymessagerespvo"></a>
<a id="tocscommonresultpageresultnotifymessagerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "userId": 25025,
        "userType": 1,
        "templateId": 13013,
        "templateCode": "test_01",
        "templateNickname": "芋艿",
        "templateContent": "测试内容",
        "templateType": 2,
        "templateParams": {
          "property1": {},
          "property2": {}
        },
        "readStatus": true,
        "readTime": "2019-08-24T14:15:22Z",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultNotifyMessageRespVO](#schemapageresultnotifymessagerespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_NotifyMessageRespVO">NotifyMessageRespVO</h2>

<a id="schemanotifymessagerespvo"></a>
<a id="schema_NotifyMessageRespVO"></a>
<a id="tocSnotifymessagerespvo"></a>
<a id="tocsnotifymessagerespvo"></a>

```json
{
  "id": 1024,
  "userId": 25025,
  "userType": 1,
  "templateId": 13013,
  "templateCode": "test_01",
  "templateNickname": "芋艿",
  "templateContent": "测试内容",
  "templateType": 2,
  "templateParams": {
    "property1": {},
    "property2": {}
  },
  "readStatus": true,
  "readTime": "2019-08-24T14:15:22Z",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 站内信 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||ID|
|userId|integer(int64)|true|none||用户编号|
|userType|string(byte)|true|none||用户类型，参见 UserTypeEnum 枚举|
|templateId|integer(int64)|true|none||模版编号|
|templateCode|string|true|none||模板编码|
|templateNickname|string|true|none||模版发送人名称|
|templateContent|string|true|none||模版内容|
|templateType|integer(int32)|true|none||模版类型|
|templateParams|object|true|none||模版参数|
|» **additionalProperties**|object|false|none||none|
|readStatus|boolean|true|none||是否已读|
|readTime|string(date-time)|false|none||阅读时间|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultNotifyMessageRespVO">PageResultNotifyMessageRespVO</h2>

<a id="schemapageresultnotifymessagerespvo"></a>
<a id="schema_PageResultNotifyMessageRespVO"></a>
<a id="tocSpageresultnotifymessagerespvo"></a>
<a id="tocspageresultnotifymessagerespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "userId": 25025,
      "userType": 1,
      "templateId": 13013,
      "templateCode": "test_01",
      "templateNickname": "芋艿",
      "templateContent": "测试内容",
      "templateType": 2,
      "templateParams": {
        "property1": {},
        "property2": {}
      },
      "readStatus": true,
      "readTime": "2019-08-24T14:15:22Z",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[NotifyMessageRespVO](#schemanotifymessagerespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultNotifyMessageRespVO">CommonResultNotifyMessageRespVO</h2>

<a id="schemacommonresultnotifymessagerespvo"></a>
<a id="schema_CommonResultNotifyMessageRespVO"></a>
<a id="tocScommonresultnotifymessagerespvo"></a>
<a id="tocscommonresultnotifymessagerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "userId": 25025,
    "userType": 1,
    "templateId": 13013,
    "templateCode": "test_01",
    "templateNickname": "芋艿",
    "templateContent": "测试内容",
    "templateType": 2,
    "templateParams": {
      "property1": {},
      "property2": {}
    },
    "readStatus": true,
    "readTime": "2019-08-24T14:15:22Z",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[NotifyMessageRespVO](#schemanotifymessagerespvo)|false|none||管理后台 - 站内信 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListNotifyMessageRespVO">CommonResultListNotifyMessageRespVO</h2>

<a id="schemacommonresultlistnotifymessagerespvo"></a>
<a id="schema_CommonResultListNotifyMessageRespVO"></a>
<a id="tocScommonresultlistnotifymessagerespvo"></a>
<a id="tocscommonresultlistnotifymessagerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "userId": 25025,
      "userType": 1,
      "templateId": 13013,
      "templateCode": "test_01",
      "templateNickname": "芋艿",
      "templateContent": "测试内容",
      "templateType": 2,
      "templateParams": {
        "property1": {},
        "property2": {}
      },
      "readStatus": true,
      "readTime": "2019-08-24T14:15:22Z",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[NotifyMessageRespVO](#schemanotifymessagerespvo)]|false|none||[管理后台 - 站内信 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultNoticeRespVO">CommonResultPageResultNoticeRespVO</h2>

<a id="schemacommonresultpageresultnoticerespvo"></a>
<a id="schema_CommonResultPageResultNoticeRespVO"></a>
<a id="tocScommonresultpageresultnoticerespvo"></a>
<a id="tocscommonresultpageresultnoticerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "title": "小博主",
        "type": "小博主",
        "content": "半生编码",
        "status": 1,
        "createTime": "时间戳格式"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultNoticeRespVO](#schemapageresultnoticerespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_NoticeRespVO">NoticeRespVO</h2>

<a id="schemanoticerespvo"></a>
<a id="schema_NoticeRespVO"></a>
<a id="tocSnoticerespvo"></a>
<a id="tocsnoticerespvo"></a>

```json
{
  "id": 1024,
  "title": "小博主",
  "type": "小博主",
  "content": "半生编码",
  "status": 1,
  "createTime": "时间戳格式"
}

```

管理后台 - 通知公告信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||通知公告序号|
|title|string|true|none||公告标题|
|type|integer(int32)|true|none||公告类型|
|content|string|true|none||公告内容|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举类|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultNoticeRespVO">PageResultNoticeRespVO</h2>

<a id="schemapageresultnoticerespvo"></a>
<a id="schema_PageResultNoticeRespVO"></a>
<a id="tocSpageresultnoticerespvo"></a>
<a id="tocspageresultnoticerespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "title": "小博主",
      "type": "小博主",
      "content": "半生编码",
      "status": 1,
      "createTime": "时间戳格式"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[NoticeRespVO](#schemanoticerespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultNoticeRespVO">CommonResultNoticeRespVO</h2>

<a id="schemacommonresultnoticerespvo"></a>
<a id="schema_CommonResultNoticeRespVO"></a>
<a id="tocScommonresultnoticerespvo"></a>
<a id="tocscommonresultnoticerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "title": "小博主",
    "type": "小博主",
    "content": "半生编码",
    "status": 1,
    "createTime": "时间戳格式"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[NoticeRespVO](#schemanoticerespvo)|false|none||管理后台 - 通知公告信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListMenuRespVO">CommonResultListMenuRespVO</h2>

<a id="schemacommonresultlistmenurespvo"></a>
<a id="schema_CommonResultListMenuRespVO"></a>
<a id="tocScommonresultlistmenurespvo"></a>
<a id="tocscommonresultlistmenurespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "芋道",
      "permission": "sys:menu:add",
      "type": 1,
      "sort": 1024,
      "parentId": 1024,
      "path": "post",
      "icon": "/menu/list",
      "component": "system/post/index",
      "componentName": "SystemUser",
      "status": 1,
      "visible": false,
      "keepAlive": false,
      "alwaysShow": false,
      "createTime": "时间戳格式"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[MenuRespVO](#schemamenurespvo)]|false|none||[管理后台 - 菜单信息 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_MenuRespVO">MenuRespVO</h2>

<a id="schemamenurespvo"></a>
<a id="schema_MenuRespVO"></a>
<a id="tocSmenurespvo"></a>
<a id="tocsmenurespvo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "permission": "sys:menu:add",
  "type": 1,
  "sort": 1024,
  "parentId": 1024,
  "path": "post",
  "icon": "/menu/list",
  "component": "system/post/index",
  "componentName": "SystemUser",
  "status": 1,
  "visible": false,
  "keepAlive": false,
  "alwaysShow": false,
  "createTime": "时间戳格式"
}

```

管理后台 - 菜单信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||菜单编号|
|name|string|true|none||菜单名称|
|permission|string|false|none||权限标识,仅菜单类型为按钮时，才需要传递|
|type|integer(int32)|true|none||类型，参见 MenuTypeEnum 枚举类|
|sort|integer(int32)|true|none||显示顺序|
|parentId|integer(int64)|true|none||父菜单 ID|
|path|string|false|none||路由地址,仅菜单类型为菜单或者目录时，才需要传|
|icon|string|false|none||菜单图标,仅菜单类型为菜单或者目录时，才需要传|
|component|string|false|none||组件路径,仅菜单类型为菜单时，才需要传|
|componentName|string|false|none||组件名|
|status|integer(int32)|true|none||状态,见 CommonStatusEnum 枚举|
|visible|boolean|false|none||是否可见|
|keepAlive|boolean|false|none||是否缓存|
|alwaysShow|boolean|false|none||是否总是显示|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultListMenuSimpleRespVO">CommonResultListMenuSimpleRespVO</h2>

<a id="schemacommonresultlistmenusimplerespvo"></a>
<a id="schema_CommonResultListMenuSimpleRespVO"></a>
<a id="tocScommonresultlistmenusimplerespvo"></a>
<a id="tocscommonresultlistmenusimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "芋道",
      "parentId": 1024,
      "type": 1
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[MenuSimpleRespVO](#schemamenusimplerespvo)]|false|none||[管理后台 - 菜单精简信息 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_MenuSimpleRespVO">MenuSimpleRespVO</h2>

<a id="schemamenusimplerespvo"></a>
<a id="schema_MenuSimpleRespVO"></a>
<a id="tocSmenusimplerespvo"></a>
<a id="tocsmenusimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "parentId": 1024,
  "type": 1
}

```

管理后台 - 菜单精简信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||菜单编号|
|name|string|true|none||菜单名称|
|parentId|integer(int64)|true|none||父菜单 ID|
|type|integer(int32)|true|none||类型，参见 MenuTypeEnum 枚举类|

<h2 id="tocS_CommonResultMenuRespVO">CommonResultMenuRespVO</h2>

<a id="schemacommonresultmenurespvo"></a>
<a id="schema_CommonResultMenuRespVO"></a>
<a id="tocScommonresultmenurespvo"></a>
<a id="tocscommonresultmenurespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "芋道",
    "permission": "sys:menu:add",
    "type": 1,
    "sort": 1024,
    "parentId": 1024,
    "path": "post",
    "icon": "/menu/list",
    "component": "system/post/index",
    "componentName": "SystemUser",
    "status": 1,
    "visible": false,
    "keepAlive": false,
    "alwaysShow": false,
    "createTime": "时间戳格式"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[MenuRespVO](#schemamenurespvo)|false|none||管理后台 - 菜单信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultMailTemplateRespVO">CommonResultPageResultMailTemplateRespVO</h2>

<a id="schemacommonresultpageresultmailtemplaterespvo"></a>
<a id="schema_CommonResultPageResultMailTemplateRespVO"></a>
<a id="tocScommonresultpageresultmailtemplaterespvo"></a>
<a id="tocscommonresultpageresultmailtemplaterespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "name": "测试名字",
        "code": "test",
        "accountId": 1,
        "nickname": "芋头",
        "title": "注册成功",
        "content": "你好，注册成功啦",
        "params": "name,code",
        "status": 1,
        "remark": "奥特曼",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultMailTemplateRespVO](#schemapageresultmailtemplaterespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_MailTemplateRespVO">MailTemplateRespVO</h2>

<a id="schemamailtemplaterespvo"></a>
<a id="schema_MailTemplateRespVO"></a>
<a id="tocSmailtemplaterespvo"></a>
<a id="tocsmailtemplaterespvo"></a>

```json
{
  "id": 1024,
  "name": "测试名字",
  "code": "test",
  "accountId": 1,
  "nickname": "芋头",
  "title": "注册成功",
  "content": "你好，注册成功啦",
  "params": "name,code",
  "status": 1,
  "remark": "奥特曼",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 邮件末班 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||模版名称|
|code|string|true|none||模版编号|
|accountId|integer(int64)|true|none||发送的邮箱账号编号|
|nickname|string|false|none||发送人名称|
|title|string|true|none||标题|
|content|string|true|none||内容|
|params|[string]|false|none||参数数组|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举|
|remark|string|false|none||备注|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultMailTemplateRespVO">PageResultMailTemplateRespVO</h2>

<a id="schemapageresultmailtemplaterespvo"></a>
<a id="schema_PageResultMailTemplateRespVO"></a>
<a id="tocSpageresultmailtemplaterespvo"></a>
<a id="tocspageresultmailtemplaterespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "name": "测试名字",
      "code": "test",
      "accountId": 1,
      "nickname": "芋头",
      "title": "注册成功",
      "content": "你好，注册成功啦",
      "params": "name,code",
      "status": 1,
      "remark": "奥特曼",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[MailTemplateRespVO](#schemamailtemplaterespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultListMailTemplateSimpleRespVO">CommonResultListMailTemplateSimpleRespVO</h2>

<a id="schemacommonresultlistmailtemplatesimplerespvo"></a>
<a id="schema_CommonResultListMailTemplateSimpleRespVO"></a>
<a id="tocScommonresultlistmailtemplatesimplerespvo"></a>
<a id="tocscommonresultlistmailtemplatesimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "哒哒哒"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[MailTemplateSimpleRespVO](#schemamailtemplatesimplerespvo)]|false|none||[管理后台 - 邮件模版的精简 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_MailTemplateSimpleRespVO">MailTemplateSimpleRespVO</h2>

<a id="schemamailtemplatesimplerespvo"></a>
<a id="schema_MailTemplateSimpleRespVO"></a>
<a id="tocSmailtemplatesimplerespvo"></a>
<a id="tocsmailtemplatesimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "哒哒哒"
}

```

管理后台 - 邮件模版的精简 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||模版编号|
|name|string|true|none||模版名字|

<h2 id="tocS_CommonResultMailTemplateRespVO">CommonResultMailTemplateRespVO</h2>

<a id="schemacommonresultmailtemplaterespvo"></a>
<a id="schema_CommonResultMailTemplateRespVO"></a>
<a id="tocScommonresultmailtemplaterespvo"></a>
<a id="tocscommonresultmailtemplaterespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "测试名字",
    "code": "test",
    "accountId": 1,
    "nickname": "芋头",
    "title": "注册成功",
    "content": "你好，注册成功啦",
    "params": "name,code",
    "status": 1,
    "remark": "奥特曼",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[MailTemplateRespVO](#schemamailtemplaterespvo)|false|none||管理后台 - 邮件末班 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultMailLogRespVO">CommonResultPageResultMailLogRespVO</h2>

<a id="schemacommonresultpageresultmaillogrespvo"></a>
<a id="schema_CommonResultPageResultMailLogRespVO"></a>
<a id="tocScommonresultpageresultmaillogrespvo"></a>
<a id="tocscommonresultpageresultmaillogrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 31020,
        "userId": 30883,
        "userType": 2,
        "toMail": "<EMAIL>",
        "accountId": 18107,
        "fromMail": "<EMAIL>",
        "templateId": 5678,
        "templateCode": "test_01",
        "templateNickname": "李四",
        "templateTitle": "测试标题",
        "templateContent": "测试内容",
        "templateParams": {
          "property1": {},
          "property2": {}
        },
        "sendStatus": 1,
        "sendTime": "2019-08-24T14:15:22Z",
        "sendMessageId": 28568,
        "sendException": "string",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultMailLogRespVO](#schemapageresultmaillogrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_MailLogRespVO">MailLogRespVO</h2>

<a id="schemamaillogrespvo"></a>
<a id="schema_MailLogRespVO"></a>
<a id="tocSmaillogrespvo"></a>
<a id="tocsmaillogrespvo"></a>

```json
{
  "id": 31020,
  "userId": 30883,
  "userType": 2,
  "toMail": "<EMAIL>",
  "accountId": 18107,
  "fromMail": "<EMAIL>",
  "templateId": 5678,
  "templateCode": "test_01",
  "templateNickname": "李四",
  "templateTitle": "测试标题",
  "templateContent": "测试内容",
  "templateParams": {
    "property1": {},
    "property2": {}
  },
  "sendStatus": 1,
  "sendTime": "2019-08-24T14:15:22Z",
  "sendMessageId": 28568,
  "sendException": "string",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 邮件日志 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|userId|integer(int64)|false|none||用户编号|
|userType|string(byte)|false|none||用户类型，参见 UserTypeEnum 枚举|
|toMail|string|true|none||接收邮箱地址|
|accountId|integer(int64)|true|none||邮箱账号编号|
|fromMail|string|true|none||发送邮箱地址|
|templateId|integer(int64)|true|none||模板编号|
|templateCode|string|true|none||模板编码|
|templateNickname|string|false|none||模版发送人名称|
|templateTitle|string|true|none||邮件标题|
|templateContent|string|true|none||邮件内容|
|templateParams|object|true|none||邮件参数|
|» **additionalProperties**|object|false|none||none|
|sendStatus|string(byte)|true|none||发送状态，参见 MailSendStatusEnum 枚举|
|sendTime|string(date-time)|false|none||发送时间|
|sendMessageId|string|false|none||发送返回的消息 ID|
|sendException|string|false|none||发送异常|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultMailLogRespVO">PageResultMailLogRespVO</h2>

<a id="schemapageresultmaillogrespvo"></a>
<a id="schema_PageResultMailLogRespVO"></a>
<a id="tocSpageresultmaillogrespvo"></a>
<a id="tocspageresultmaillogrespvo"></a>

```json
{
  "list": [
    {
      "id": 31020,
      "userId": 30883,
      "userType": 2,
      "toMail": "<EMAIL>",
      "accountId": 18107,
      "fromMail": "<EMAIL>",
      "templateId": 5678,
      "templateCode": "test_01",
      "templateNickname": "李四",
      "templateTitle": "测试标题",
      "templateContent": "测试内容",
      "templateParams": {
        "property1": {},
        "property2": {}
      },
      "sendStatus": 1,
      "sendTime": "2019-08-24T14:15:22Z",
      "sendMessageId": 28568,
      "sendException": "string",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[MailLogRespVO](#schemamaillogrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultMailLogRespVO">CommonResultMailLogRespVO</h2>

<a id="schemacommonresultmaillogrespvo"></a>
<a id="schema_CommonResultMailLogRespVO"></a>
<a id="tocScommonresultmaillogrespvo"></a>
<a id="tocscommonresultmaillogrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 31020,
    "userId": 30883,
    "userType": 2,
    "toMail": "<EMAIL>",
    "accountId": 18107,
    "fromMail": "<EMAIL>",
    "templateId": 5678,
    "templateCode": "test_01",
    "templateNickname": "李四",
    "templateTitle": "测试标题",
    "templateContent": "测试内容",
    "templateParams": {
      "property1": {},
      "property2": {}
    },
    "sendStatus": 1,
    "sendTime": "2019-08-24T14:15:22Z",
    "sendMessageId": 28568,
    "sendException": "string",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[MailLogRespVO](#schemamaillogrespvo)|false|none||管理后台 - 邮件日志 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultMailAccountRespVO">CommonResultPageResultMailAccountRespVO</h2>

<a id="schemacommonresultpageresultmailaccountrespvo"></a>
<a id="schema_CommonResultPageResultMailAccountRespVO"></a>
<a id="tocScommonresultpageresultmailaccountrespvo"></a>
<a id="tocscommonresultpageresultmailaccountrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "mail": "<EMAIL>",
        "username": "yudao",
        "password": 123456,
        "host": "www.iocoder.cn",
        "port": 80,
        "sslEnable": true,
        "starttlsEnable": true,
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultMailAccountRespVO](#schemapageresultmailaccountrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_MailAccountRespVO">MailAccountRespVO</h2>

<a id="schemamailaccountrespvo"></a>
<a id="schema_MailAccountRespVO"></a>
<a id="tocSmailaccountrespvo"></a>
<a id="tocsmailaccountrespvo"></a>

```json
{
  "id": 1024,
  "mail": "<EMAIL>",
  "username": "yudao",
  "password": 123456,
  "host": "www.iocoder.cn",
  "port": 80,
  "sslEnable": true,
  "starttlsEnable": true,
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 邮箱账号 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|mail|string|true|none||邮箱|
|username|string|true|none||用户名|
|password|string|true|none||密码|
|host|string|true|none||SMTP 服务器域名|
|port|integer(int32)|true|none||SMTP 服务器端口|
|sslEnable|boolean|true|none||是否开启 ssl|
|starttlsEnable|boolean|true|none||是否开启 starttls|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultMailAccountRespVO">PageResultMailAccountRespVO</h2>

<a id="schemapageresultmailaccountrespvo"></a>
<a id="schema_PageResultMailAccountRespVO"></a>
<a id="tocSpageresultmailaccountrespvo"></a>
<a id="tocspageresultmailaccountrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "mail": "<EMAIL>",
      "username": "yudao",
      "password": 123456,
      "host": "www.iocoder.cn",
      "port": 80,
      "sslEnable": true,
      "starttlsEnable": true,
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[MailAccountRespVO](#schemamailaccountrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultListMailAccountSimpleRespVO">CommonResultListMailAccountSimpleRespVO</h2>

<a id="schemacommonresultlistmailaccountsimplerespvo"></a>
<a id="schema_CommonResultListMailAccountSimpleRespVO"></a>
<a id="tocScommonresultlistmailaccountsimplerespvo"></a>
<a id="tocscommonresultlistmailaccountsimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "mail": "<EMAIL>"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[MailAccountSimpleRespVO](#schemamailaccountsimplerespvo)]|false|none||[管理后台 - 邮箱账号的精简 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_MailAccountSimpleRespVO">MailAccountSimpleRespVO</h2>

<a id="schemamailaccountsimplerespvo"></a>
<a id="schema_MailAccountSimpleRespVO"></a>
<a id="tocSmailaccountsimplerespvo"></a>
<a id="tocsmailaccountsimplerespvo"></a>

```json
{
  "id": 1024,
  "mail": "<EMAIL>"
}

```

管理后台 - 邮箱账号的精简 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||邮箱编号|
|mail|string|true|none||邮箱|

<h2 id="tocS_CommonResultMailAccountRespVO">CommonResultMailAccountRespVO</h2>

<a id="schemacommonresultmailaccountrespvo"></a>
<a id="schema_CommonResultMailAccountRespVO"></a>
<a id="tocScommonresultmailaccountrespvo"></a>
<a id="tocscommonresultmailaccountrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "mail": "<EMAIL>",
    "username": "yudao",
    "password": 123456,
    "host": "www.iocoder.cn",
    "port": 80,
    "sslEnable": true,
    "starttlsEnable": true,
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[MailAccountRespVO](#schemamailaccountrespvo)|false|none||管理后台 - 邮箱账号 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultLoginLogRespVO">CommonResultPageResultLoginLogRespVO</h2>

<a id="schemacommonresultpageresultloginlogrespvo"></a>
<a id="schema_CommonResultPageResultLoginLogRespVO"></a>
<a id="tocScommonresultpageresultloginlogrespvo"></a>
<a id="tocscommonresultpageresultloginlogrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "logType": 1,
        "userId": 666,
        "userType": 2,
        "traceId": "89aca178-a370-411c-ae02-3f0d672be4ab",
        "username": "yudao",
        "result": 1,
        "userIp": "127.0.0.1",
        "userAgent": "Mozilla/5.0",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultLoginLogRespVO](#schemapageresultloginlogrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_LoginLogRespVO">LoginLogRespVO</h2>

<a id="schemaloginlogrespvo"></a>
<a id="schema_LoginLogRespVO"></a>
<a id="tocSloginlogrespvo"></a>
<a id="tocsloginlogrespvo"></a>

```json
{
  "id": 1024,
  "logType": 1,
  "userId": 666,
  "userType": 2,
  "traceId": "89aca178-a370-411c-ae02-3f0d672be4ab",
  "username": "yudao",
  "result": 1,
  "userIp": "127.0.0.1",
  "userAgent": "Mozilla/5.0",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 登录日志 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||日志编号|
|logType|integer(int32)|true|none||日志类型，参见 LoginLogTypeEnum 枚举类|
|userId|integer(int64)|false|none||用户编号|
|userType|integer(int32)|true|none||用户类型，参见 UserTypeEnum 枚举|
|traceId|string|false|none||链路追踪编号|
|username|string|true|none||用户账号|
|result|integer(int32)|true|none||登录结果，参见 LoginResultEnum 枚举类|
|userIp|string|true|none||用户 IP|
|userAgent|string|false|none||浏览器 UserAgent|
|createTime|string(date-time)|true|none||登录时间|

<h2 id="tocS_PageResultLoginLogRespVO">PageResultLoginLogRespVO</h2>

<a id="schemapageresultloginlogrespvo"></a>
<a id="schema_PageResultLoginLogRespVO"></a>
<a id="tocSpageresultloginlogrespvo"></a>
<a id="tocspageresultloginlogrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "logType": 1,
      "userId": 666,
      "userType": 2,
      "traceId": "89aca178-a370-411c-ae02-3f0d672be4ab",
      "username": "yudao",
      "result": 1,
      "userIp": "127.0.0.1",
      "userAgent": "Mozilla/5.0",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[LoginLogRespVO](#schemaloginlogrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultPageResultDictTypeRespVO">CommonResultPageResultDictTypeRespVO</h2>

<a id="schemacommonresultpageresultdicttyperespvo"></a>
<a id="schema_CommonResultPageResultDictTypeRespVO"></a>
<a id="tocScommonresultpageresultdicttyperespvo"></a>
<a id="tocscommonresultpageresultdicttyperespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "name": "性别",
        "type": "sys_common_sex",
        "status": 1,
        "remark": "快乐的备注",
        "createTime": "时间戳格式"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultDictTypeRespVO](#schemapageresultdicttyperespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_DictTypeRespVO">DictTypeRespVO</h2>

<a id="schemadicttyperespvo"></a>
<a id="schema_DictTypeRespVO"></a>
<a id="tocSdicttyperespvo"></a>
<a id="tocsdicttyperespvo"></a>

```json
{
  "id": 1024,
  "name": "性别",
  "type": "sys_common_sex",
  "status": 1,
  "remark": "快乐的备注",
  "createTime": "时间戳格式"
}

```

管理后台 - 字典类型信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||字典类型编号|
|name|string|true|none||字典名称|
|type|string|true|none||字典类型|
|status|integer(int32)|true|none||状态，参见 CommonStatusEnum 枚举类|
|remark|string|false|none||备注|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultDictTypeRespVO">PageResultDictTypeRespVO</h2>

<a id="schemapageresultdicttyperespvo"></a>
<a id="schema_PageResultDictTypeRespVO"></a>
<a id="tocSpageresultdicttyperespvo"></a>
<a id="tocspageresultdicttyperespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "name": "性别",
      "type": "sys_common_sex",
      "status": 1,
      "remark": "快乐的备注",
      "createTime": "时间戳格式"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[DictTypeRespVO](#schemadicttyperespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultListDictTypeSimpleRespVO">CommonResultListDictTypeSimpleRespVO</h2>

<a id="schemacommonresultlistdicttypesimplerespvo"></a>
<a id="schema_CommonResultListDictTypeSimpleRespVO"></a>
<a id="tocScommonresultlistdicttypesimplerespvo"></a>
<a id="tocscommonresultlistdicttypesimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "芋道",
      "type": "sys_common_sex"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[DictTypeSimpleRespVO](#schemadicttypesimplerespvo)]|false|none||[管理后台 - 字典类型精简信息 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_DictTypeSimpleRespVO">DictTypeSimpleRespVO</h2>

<a id="schemadicttypesimplerespvo"></a>
<a id="schema_DictTypeSimpleRespVO"></a>
<a id="tocSdicttypesimplerespvo"></a>
<a id="tocsdicttypesimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "type": "sys_common_sex"
}

```

管理后台 - 字典类型精简信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||字典类型编号|
|name|string|true|none||字典类型名称|
|type|string|true|none||字典类型|

<h2 id="tocS_CommonResultDictTypeRespVO">CommonResultDictTypeRespVO</h2>

<a id="schemacommonresultdicttyperespvo"></a>
<a id="schema_CommonResultDictTypeRespVO"></a>
<a id="tocScommonresultdicttyperespvo"></a>
<a id="tocscommonresultdicttyperespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "性别",
    "type": "sys_common_sex",
    "status": 1,
    "remark": "快乐的备注",
    "createTime": "时间戳格式"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[DictTypeRespVO](#schemadicttyperespvo)|false|none||管理后台 - 字典类型信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultDictDataRespVO">CommonResultPageResultDictDataRespVO</h2>

<a id="schemacommonresultpageresultdictdatarespvo"></a>
<a id="schema_CommonResultPageResultDictDataRespVO"></a>
<a id="tocScommonresultpageresultdictdatarespvo"></a>
<a id="tocscommonresultpageresultdictdatarespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "sort": 1024,
        "label": "芋道",
        "value": "iocoder",
        "dictType": "sys_common_sex",
        "status": 1,
        "colorType": "default",
        "cssClass": "btn-visible",
        "remark": "我是一个角色",
        "createTime": "时间戳格式"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultDictDataRespVO](#schemapageresultdictdatarespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_DictDataRespVO">DictDataRespVO</h2>

<a id="schemadictdatarespvo"></a>
<a id="schema_DictDataRespVO"></a>
<a id="tocSdictdatarespvo"></a>
<a id="tocsdictdatarespvo"></a>

```json
{
  "id": 1024,
  "sort": 1024,
  "label": "芋道",
  "value": "iocoder",
  "dictType": "sys_common_sex",
  "status": 1,
  "colorType": "default",
  "cssClass": "btn-visible",
  "remark": "我是一个角色",
  "createTime": "时间戳格式"
}

```

管理后台 - 字典数据信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||字典数据编号|
|sort|integer(int32)|true|none||显示顺序|
|label|string|true|none||字典标签|
|value|string|true|none||字典值|
|dictType|string|true|none||字典类型|
|status|integer(int32)|true|none||状态,见 CommonStatusEnum 枚举|
|colorType|string|false|none||颜色类型,default、primary、success、info、warning、danger|
|cssClass|string|false|none||css 样式|
|remark|string|false|none||备注|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultDictDataRespVO">PageResultDictDataRespVO</h2>

<a id="schemapageresultdictdatarespvo"></a>
<a id="schema_PageResultDictDataRespVO"></a>
<a id="tocSpageresultdictdatarespvo"></a>
<a id="tocspageresultdictdatarespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "sort": 1024,
      "label": "芋道",
      "value": "iocoder",
      "dictType": "sys_common_sex",
      "status": 1,
      "colorType": "default",
      "cssClass": "btn-visible",
      "remark": "我是一个角色",
      "createTime": "时间戳格式"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[DictDataRespVO](#schemadictdatarespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultListDictDataSimpleRespVO">CommonResultListDictDataSimpleRespVO</h2>

<a id="schemacommonresultlistdictdatasimplerespvo"></a>
<a id="schema_CommonResultListDictDataSimpleRespVO"></a>
<a id="tocScommonresultlistdictdatasimplerespvo"></a>
<a id="tocscommonresultlistdictdatasimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "dictType": "gender",
      "value": 1,
      "label": "男",
      "colorType": "default",
      "cssClass": "btn-visible"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[DictDataSimpleRespVO](#schemadictdatasimplerespvo)]|false|none||[管理后台 - 数据字典精简 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_DictDataSimpleRespVO">DictDataSimpleRespVO</h2>

<a id="schemadictdatasimplerespvo"></a>
<a id="schema_DictDataSimpleRespVO"></a>
<a id="tocSdictdatasimplerespvo"></a>
<a id="tocsdictdatasimplerespvo"></a>

```json
{
  "dictType": "gender",
  "value": 1,
  "label": "男",
  "colorType": "default",
  "cssClass": "btn-visible"
}

```

管理后台 - 数据字典精简 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|dictType|string|true|none||字典类型|
|value|string|true|none||字典键值|
|label|string|true|none||字典标签|
|colorType|string|false|none||颜色类型，default、primary、success、info、warning、danger|
|cssClass|string|false|none||css 样式|

<h2 id="tocS_CommonResultDictDataRespVO">CommonResultDictDataRespVO</h2>

<a id="schemacommonresultdictdatarespvo"></a>
<a id="schema_CommonResultDictDataRespVO"></a>
<a id="tocScommonresultdictdatarespvo"></a>
<a id="tocscommonresultdictdatarespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "sort": 1024,
    "label": "芋道",
    "value": "iocoder",
    "dictType": "sys_common_sex",
    "status": 1,
    "colorType": "default",
    "cssClass": "btn-visible",
    "remark": "我是一个角色",
    "createTime": "时间戳格式"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[DictDataRespVO](#schemadictdatarespvo)|false|none||管理后台 - 字典数据信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListDeptRespVO">CommonResultListDeptRespVO</h2>

<a id="schemacommonresultlistdeptrespvo"></a>
<a id="schema_CommonResultListDeptRespVO"></a>
<a id="tocScommonresultlistdeptrespvo"></a>
<a id="tocscommonresultlistdeptrespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "芋道",
      "parentId": 1024,
      "sort": 1024,
      "leaderUserId": 2048,
      "phone": 15601691000,
      "email": "<EMAIL>",
      "status": 1,
      "createTime": "时间戳格式"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[DeptRespVO](#schemadeptrespvo)]|false|none||[管理后台 - 部门信息 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_DeptRespVO">DeptRespVO</h2>

<a id="schemadeptrespvo"></a>
<a id="schema_DeptRespVO"></a>
<a id="tocSdeptrespvo"></a>
<a id="tocsdeptrespvo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "parentId": 1024,
  "sort": 1024,
  "leaderUserId": 2048,
  "phone": 15601691000,
  "email": "<EMAIL>",
  "status": 1,
  "createTime": "时间戳格式"
}

```

管理后台 - 部门信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||部门编号|
|name|string|true|none||部门名称|
|parentId|integer(int64)|false|none||父部门 ID|
|sort|integer(int32)|true|none||显示顺序|
|leaderUserId|integer(int64)|false|none||负责人的用户编号|
|phone|string|false|none||联系电话|
|email|string|false|none||邮箱|
|status|integer(int32)|true|none||状态,见 CommonStatusEnum 枚举|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultListDeptSimpleRespVO">CommonResultListDeptSimpleRespVO</h2>

<a id="schemacommonresultlistdeptsimplerespvo"></a>
<a id="schema_CommonResultListDeptSimpleRespVO"></a>
<a id="tocScommonresultlistdeptsimplerespvo"></a>
<a id="tocscommonresultlistdeptsimplerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "芋道",
      "parentId": 1024
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[DeptSimpleRespVO](#schemadeptsimplerespvo)]|false|none||[管理后台 - 部门精简信息 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultDeptRespVO">CommonResultDeptRespVO</h2>

<a id="schemacommonresultdeptrespvo"></a>
<a id="schema_CommonResultDeptRespVO"></a>
<a id="tocScommonresultdeptrespvo"></a>
<a id="tocscommonresultdeptrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "芋道",
    "parentId": 1024,
    "sort": 1024,
    "leaderUserId": 2048,
    "phone": 15601691000,
    "email": "<EMAIL>",
    "status": 1,
    "createTime": "时间戳格式"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[DeptRespVO](#schemadeptrespvo)|false|none||管理后台 - 部门信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_AuthPermissionInfoRespVO">AuthPermissionInfoRespVO</h2>

<a id="schemaauthpermissioninforespvo"></a>
<a id="schema_AuthPermissionInfoRespVO"></a>
<a id="tocSauthpermissioninforespvo"></a>
<a id="tocsauthpermissioninforespvo"></a>

```json
{
  "user": {
    "id": 1024,
    "nickname": "芋道源码",
    "avatar": "https://www.iocoder.cn/xx.jpg",
    "deptId": 2048,
    "username": "yudao",
    "email": "<EMAIL>"
  },
  "roles": [
    "string"
  ],
  "permissions": [
    "string"
  ],
  "menus": [
    {
      "id": "芋道",
      "parentId": 1024,
      "name": "芋道",
      "path": "post",
      "component": "system/post/index",
      "componentName": "SystemUser",
      "icon": "/menu/list",
      "visible": false,
      "keepAlive": false,
      "alwaysShow": false
    }
  ]
}

```

管理后台 - 登录用户的权限信息 Response VO，额外包括用户信息和角色列表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|user|[UserVO](#schemauservo)|true|none||用户信息|
|roles|[string]|true|none||角色标识数组|
|permissions|[string]|true|none||操作权限数组|
|menus|[[MenuVO](#schemamenuvo)]|true|none||菜单树|

<h2 id="tocS_CommonResultAuthPermissionInfoRespVO">CommonResultAuthPermissionInfoRespVO</h2>

<a id="schemacommonresultauthpermissioninforespvo"></a>
<a id="schema_CommonResultAuthPermissionInfoRespVO"></a>
<a id="tocScommonresultauthpermissioninforespvo"></a>
<a id="tocscommonresultauthpermissioninforespvo"></a>

```json
{
  "code": 0,
  "data": {
    "user": {
      "id": 1024,
      "nickname": "芋道源码",
      "avatar": "https://www.iocoder.cn/xx.jpg",
      "deptId": 2048,
      "username": "yudao",
      "email": "<EMAIL>"
    },
    "roles": [
      "string"
    ],
    "permissions": [
      "string"
    ],
    "menus": [
      {
        "id": "芋道",
        "parentId": 1024,
        "name": "芋道",
        "path": "post",
        "component": "system/post/index",
        "componentName": "SystemUser",
        "icon": "/menu/list",
        "visible": false,
        "keepAlive": false,
        "alwaysShow": false
      }
    ]
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[AuthPermissionInfoRespVO](#schemaauthpermissioninforespvo)|false|none||管理后台 - 登录用户的权限信息 Response VO，额外包括用户信息和角色列表|
|msg|string|false|none||none|

<h2 id="tocS_MenuVO">MenuVO</h2>

<a id="schemamenuvo"></a>
<a id="schema_MenuVO"></a>
<a id="tocSmenuvo"></a>
<a id="tocsmenuvo"></a>

```json
{
  "id": "芋道",
  "parentId": 1024,
  "name": "芋道",
  "path": "post",
  "component": "system/post/index",
  "componentName": "SystemUser",
  "icon": "/menu/list",
  "visible": false,
  "keepAlive": false,
  "alwaysShow": false
}

```

管理后台 - 登录用户的菜单信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||菜单名称|
|parentId|integer(int64)|true|none||父菜单 ID|
|name|string|true|none||菜单名称|
|path|string|false|none||路由地址,仅菜单类型为菜单或者目录时，才需要传|
|component|string|false|none||组件路径,仅菜单类型为菜单时，才需要传|
|componentName|string|false|none||组件名|
|icon|string|false|none||菜单图标,仅菜单类型为菜单或者目录时，才需要传|
|visible|boolean|true|none||是否可见|
|keepAlive|boolean|true|none||是否缓存|
|alwaysShow|boolean|false|none||是否总是显示|

<h2 id="tocS_UserVO">UserVO</h2>

<a id="schemauservo"></a>
<a id="schema_UserVO"></a>
<a id="tocSuservo"></a>
<a id="tocsuservo"></a>

```json
{
  "id": 1024,
  "nickname": "芋道源码",
  "avatar": "https://www.iocoder.cn/xx.jpg",
  "deptId": 2048,
  "username": "yudao",
  "email": "<EMAIL>"
}

```

用户信息 VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||用户编号|
|nickname|string|true|none||用户昵称|
|avatar|string|true|none||用户头像|
|deptId|integer(int64)|true|none||部门编号|
|username|string|true|none||用户账号|
|email|string|false|none||用户邮箱|

<h2 id="tocS_AreaNodeRespVO">AreaNodeRespVO</h2>

<a id="schemaareanoderespvo"></a>
<a id="schema_AreaNodeRespVO"></a>
<a id="tocSareanoderespvo"></a>
<a id="tocsareanoderespvo"></a>

```json
{
  "id": 110000,
  "name": "北京"
}

```

管理后台 - 地区节点 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int32)|true|none||编号|
|name|string|true|none||名字|

<h2 id="tocS_CommonResultListAreaNodeRespVO">CommonResultListAreaNodeRespVO</h2>

<a id="schemacommonresultlistareanoderespvo"></a>
<a id="schema_CommonResultListAreaNodeRespVO"></a>
<a id="tocScommonresultlistareanoderespvo"></a>
<a id="tocscommonresultlistareanoderespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 110000,
      "name": "北京"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[AreaNodeRespVO](#schemaareanoderespvo)]|false|none||[管理后台 - 地区节点 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_CommandStat">CommandStat</h2>

<a id="schemacommandstat"></a>
<a id="schema_CommandStat"></a>
<a id="tocScommandstat"></a>
<a id="tocscommandstat"></a>

```json
{
  "command": "get",
  "calls": 1024,
  "usec": 666
}

```

Redis 命令统计结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|command|string|true|none||Redis 命令|
|calls|integer(int64)|true|none||调用次数|
|usec|integer(int64)|true|none||消耗 CPU 秒数|

<h2 id="tocS_CommonResultRedisMonitorRespVO">CommonResultRedisMonitorRespVO</h2>

<a id="schemacommonresultredismonitorrespvo"></a>
<a id="schema_CommonResultRedisMonitorRespVO"></a>
<a id="tocScommonresultredismonitorrespvo"></a>
<a id="tocscommonresultredismonitorrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "info": {
      "property1": "string",
      "property2": "string"
    },
    "dbSize": 1024,
    "commandStats": [
      {
        "command": "get",
        "calls": 1024,
        "usec": 666
      }
    ]
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[RedisMonitorRespVO](#schemaredismonitorrespvo)|false|none||管理后台 - Redis 监控信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_RedisMonitorRespVO">RedisMonitorRespVO</h2>

<a id="schemaredismonitorrespvo"></a>
<a id="schema_RedisMonitorRespVO"></a>
<a id="tocSredismonitorrespvo"></a>
<a id="tocsredismonitorrespvo"></a>

```json
{
  "info": {
    "property1": "string",
    "property2": "string"
  },
  "dbSize": 1024,
  "commandStats": [
    {
      "command": "get",
      "calls": 1024,
      "usec": 666
    }
  ]
}

```

管理后台 - Redis 监控信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|info|object|true|none||Redis info 指令结果,具体字段，查看 Redis 文档|
|» **additionalProperties**|string|false|none||none|
|dbSize|integer(int64)|true|none||Redis key 数量|
|commandStats|[[CommandStat](#schemacommandstat)]|true|none||CommandStat 数组|

<h2 id="tocS_CommonResultPageResultJobRespVO">CommonResultPageResultJobRespVO</h2>

<a id="schemacommonresultpageresultjobrespvo"></a>
<a id="schema_CommonResultPageResultJobRespVO"></a>
<a id="tocScommonresultpageresultjobrespvo"></a>
<a id="tocscommonresultpageresultjobrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "name": "测试任务",
        "status": 1,
        "handlerName": "sysUserSessionTimeoutJob",
        "handlerParam": "yudao",
        "cronExpression": "0/10 * * * * ? *",
        "retryCount": 3,
        "retryInterval": 1000,
        "monitorTimeout": 1000,
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultJobRespVO](#schemapageresultjobrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_JobRespVO">JobRespVO</h2>

<a id="schemajobrespvo"></a>
<a id="schema_JobRespVO"></a>
<a id="tocSjobrespvo"></a>
<a id="tocsjobrespvo"></a>

```json
{
  "id": 1024,
  "name": "测试任务",
  "status": 1,
  "handlerName": "sysUserSessionTimeoutJob",
  "handlerParam": "yudao",
  "cronExpression": "0/10 * * * * ? *",
  "retryCount": 3,
  "retryInterval": 1000,
  "monitorTimeout": 1000,
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 定时任务 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||任务编号|
|name|string|true|none||任务名称|
|status|integer(int32)|true|none||任务状态|
|handlerName|string|true|none||处理器的名字|
|handlerParam|string|false|none||处理器的参数|
|cronExpression|string|true|none||CRON 表达式|
|retryCount|integer(int32)|true|none||重试次数|
|retryInterval|integer(int32)|true|none||重试间隔|
|monitorTimeout|integer(int32)|false|none||监控超时时间|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultJobRespVO">PageResultJobRespVO</h2>

<a id="schemapageresultjobrespvo"></a>
<a id="schema_PageResultJobRespVO"></a>
<a id="tocSpageresultjobrespvo"></a>
<a id="tocspageresultjobrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "name": "测试任务",
      "status": 1,
      "handlerName": "sysUserSessionTimeoutJob",
      "handlerParam": "yudao",
      "cronExpression": "0/10 * * * * ? *",
      "retryCount": 3,
      "retryInterval": 1000,
      "monitorTimeout": 1000,
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[JobRespVO](#schemajobrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultListLocalDateTime">CommonResultListLocalDateTime</h2>

<a id="schemacommonresultlistlocaldatetime"></a>
<a id="schema_CommonResultListLocalDateTime"></a>
<a id="tocScommonresultlistlocaldatetime"></a>
<a id="tocscommonresultlistlocaldatetime"></a>

```json
{
  "code": 0,
  "data": [
    "2019-08-24T14:15:22Z"
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[string]|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultJobRespVO">CommonResultJobRespVO</h2>

<a id="schemacommonresultjobrespvo"></a>
<a id="schema_CommonResultJobRespVO"></a>
<a id="tocScommonresultjobrespvo"></a>
<a id="tocscommonresultjobrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "测试任务",
    "status": 1,
    "handlerName": "sysUserSessionTimeoutJob",
    "handlerParam": "yudao",
    "cronExpression": "0/10 * * * * ? *",
    "retryCount": 3,
    "retryInterval": 1000,
    "monitorTimeout": 1000,
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[JobRespVO](#schemajobrespvo)|false|none||管理后台 - 定时任务 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultJobLogRespVO">CommonResultPageResultJobLogRespVO</h2>

<a id="schemacommonresultpageresultjoblogrespvo"></a>
<a id="schema_CommonResultPageResultJobLogRespVO"></a>
<a id="tocScommonresultpageresultjoblogrespvo"></a>
<a id="tocscommonresultpageresultjoblogrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "jobId": 1024,
        "handlerName": "sysUserSessionTimeoutJob",
        "handlerParam": "yudao",
        "executeIndex": 1,
        "beginTime": "2019-08-24T14:15:22Z",
        "endTime": "2019-08-24T14:15:22Z",
        "duration": 123,
        "status": 1,
        "result": "执行成功",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultJobLogRespVO](#schemapageresultjoblogrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_JobLogRespVO">JobLogRespVO</h2>

<a id="schemajoblogrespvo"></a>
<a id="schema_JobLogRespVO"></a>
<a id="tocSjoblogrespvo"></a>
<a id="tocsjoblogrespvo"></a>

```json
{
  "id": 1024,
  "jobId": 1024,
  "handlerName": "sysUserSessionTimeoutJob",
  "handlerParam": "yudao",
  "executeIndex": 1,
  "beginTime": "2019-08-24T14:15:22Z",
  "endTime": "2019-08-24T14:15:22Z",
  "duration": 123,
  "status": 1,
  "result": "执行成功",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 定时任务日志 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||日志编号|
|jobId|integer(int64)|true|none||任务编号|
|handlerName|string|true|none||处理器的名字|
|handlerParam|string|false|none||处理器的参数|
|executeIndex|integer(int32)|true|none||第几次执行|
|beginTime|string(date-time)|true|none||开始执行时间|
|endTime|string(date-time)|false|none||结束执行时间|
|duration|integer(int32)|false|none||执行时长|
|status|integer(int32)|true|none||任务状态，参见 JobLogStatusEnum 枚举|
|result|string|false|none||结果数据|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultJobLogRespVO">PageResultJobLogRespVO</h2>

<a id="schemapageresultjoblogrespvo"></a>
<a id="schema_PageResultJobLogRespVO"></a>
<a id="tocSpageresultjoblogrespvo"></a>
<a id="tocspageresultjoblogrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "jobId": 1024,
      "handlerName": "sysUserSessionTimeoutJob",
      "handlerParam": "yudao",
      "executeIndex": 1,
      "beginTime": "2019-08-24T14:15:22Z",
      "endTime": "2019-08-24T14:15:22Z",
      "duration": 123,
      "status": 1,
      "result": "执行成功",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[JobLogRespVO](#schemajoblogrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultJobLogRespVO">CommonResultJobLogRespVO</h2>

<a id="schemacommonresultjoblogrespvo"></a>
<a id="schema_CommonResultJobLogRespVO"></a>
<a id="tocScommonresultjoblogrespvo"></a>
<a id="tocscommonresultjoblogrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "jobId": 1024,
    "handlerName": "sysUserSessionTimeoutJob",
    "handlerParam": "yudao",
    "executeIndex": 1,
    "beginTime": "2019-08-24T14:15:22Z",
    "endTime": "2019-08-24T14:15:22Z",
    "duration": 123,
    "status": 1,
    "result": "执行成功",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[JobLogRespVO](#schemajoblogrespvo)|false|none||管理后台 - 定时任务日志 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultDemo03StudentNormalRespVO">CommonResultPageResultDemo03StudentNormalRespVO</h2>

<a id="schemacommonresultpageresultdemo03studentnormalrespvo"></a>
<a id="schema_CommonResultPageResultDemo03StudentNormalRespVO"></a>
<a id="tocScommonresultpageresultdemo03studentnormalrespvo"></a>
<a id="tocscommonresultpageresultdemo03studentnormalrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 8525,
        "name": "芋艿",
        "sex": 0,
        "birthday": "2019-08-24T14:15:22Z",
        "description": "随便",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultDemo03StudentNormalRespVO](#schemapageresultdemo03studentnormalrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_Demo03StudentNormalRespVO">Demo03StudentNormalRespVO</h2>

<a id="schemademo03studentnormalrespvo"></a>
<a id="schema_Demo03StudentNormalRespVO"></a>
<a id="tocSdemo03studentnormalrespvo"></a>
<a id="tocsdemo03studentnormalrespvo"></a>

```json
{
  "id": 8525,
  "name": "芋艿",
  "sex": 0,
  "birthday": "2019-08-24T14:15:22Z",
  "description": "随便",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 学生 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|sex|integer(int32)|true|none||性别|
|birthday|string(date-time)|true|none||出生日期|
|description|string|true|none||简介|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultDemo03StudentNormalRespVO">PageResultDemo03StudentNormalRespVO</h2>

<a id="schemapageresultdemo03studentnormalrespvo"></a>
<a id="schema_PageResultDemo03StudentNormalRespVO"></a>
<a id="tocSpageresultdemo03studentnormalrespvo"></a>
<a id="tocspageresultdemo03studentnormalrespvo"></a>

```json
{
  "list": [
    {
      "id": 8525,
      "name": "芋艿",
      "sex": 0,
      "birthday": "2019-08-24T14:15:22Z",
      "description": "随便",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[Demo03StudentNormalRespVO](#schemademo03studentnormalrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultDemo03StudentNormalRespVO">CommonResultDemo03StudentNormalRespVO</h2>

<a id="schemacommonresultdemo03studentnormalrespvo"></a>
<a id="schema_CommonResultDemo03StudentNormalRespVO"></a>
<a id="tocScommonresultdemo03studentnormalrespvo"></a>
<a id="tocscommonresultdemo03studentnormalrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 8525,
    "name": "芋艿",
    "sex": 0,
    "birthday": "2019-08-24T14:15:22Z",
    "description": "随便",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[Demo03StudentNormalRespVO](#schemademo03studentnormalrespvo)|false|none||管理后台 - 学生 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultDemo03GradeDO">CommonResultDemo03GradeDO</h2>

<a id="schemacommonresultdemo03gradedo"></a>
<a id="schema_CommonResultDemo03GradeDO"></a>
<a id="tocScommonresultdemo03gradedo"></a>
<a id="tocscommonresultdemo03gradedo"></a>

```json
{
  "code": 0,
  "data": {
    "createTime": "2019-08-24T14:15:22Z",
    "updateTime": "2019-08-24T14:15:22Z",
    "creator": "string",
    "updater": "string",
    "deleted": true,
    "id": 0,
    "studentId": 0,
    "name": "string",
    "teacher": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[Demo03GradeDO](#schemademo03gradedo)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListDemo03CourseDO">CommonResultListDemo03CourseDO</h2>

<a id="schemacommonresultlistdemo03coursedo"></a>
<a id="schema_CommonResultListDemo03CourseDO"></a>
<a id="tocScommonresultlistdemo03coursedo"></a>
<a id="tocscommonresultlistdemo03coursedo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z",
      "creator": "string",
      "updater": "string",
      "deleted": true,
      "id": 0,
      "studentId": 0,
      "name": "string",
      "score": 0
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[Demo03CourseDO](#schemademo03coursedo)]|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultDemo03StudentInnerRespVO">CommonResultPageResultDemo03StudentInnerRespVO</h2>

<a id="schemacommonresultpageresultdemo03studentinnerrespvo"></a>
<a id="schema_CommonResultPageResultDemo03StudentInnerRespVO"></a>
<a id="tocScommonresultpageresultdemo03studentinnerrespvo"></a>
<a id="tocscommonresultpageresultdemo03studentinnerrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 8525,
        "name": "芋艿",
        "sex": 0,
        "birthday": "2019-08-24T14:15:22Z",
        "description": "随便",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultDemo03StudentInnerRespVO](#schemapageresultdemo03studentinnerrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_Demo03StudentInnerRespVO">Demo03StudentInnerRespVO</h2>

<a id="schemademo03studentinnerrespvo"></a>
<a id="schema_Demo03StudentInnerRespVO"></a>
<a id="tocSdemo03studentinnerrespvo"></a>
<a id="tocsdemo03studentinnerrespvo"></a>

```json
{
  "id": 8525,
  "name": "芋艿",
  "sex": 0,
  "birthday": "2019-08-24T14:15:22Z",
  "description": "随便",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 学生 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|sex|integer(int32)|true|none||性别|
|birthday|string(date-time)|true|none||出生日期|
|description|string|true|none||简介|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultDemo03StudentInnerRespVO">PageResultDemo03StudentInnerRespVO</h2>

<a id="schemapageresultdemo03studentinnerrespvo"></a>
<a id="schema_PageResultDemo03StudentInnerRespVO"></a>
<a id="tocSpageresultdemo03studentinnerrespvo"></a>
<a id="tocspageresultdemo03studentinnerrespvo"></a>

```json
{
  "list": [
    {
      "id": 8525,
      "name": "芋艿",
      "sex": 0,
      "birthday": "2019-08-24T14:15:22Z",
      "description": "随便",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[Demo03StudentInnerRespVO](#schemademo03studentinnerrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultDemo03StudentInnerRespVO">CommonResultDemo03StudentInnerRespVO</h2>

<a id="schemacommonresultdemo03studentinnerrespvo"></a>
<a id="schema_CommonResultDemo03StudentInnerRespVO"></a>
<a id="tocScommonresultdemo03studentinnerrespvo"></a>
<a id="tocscommonresultdemo03studentinnerrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 8525,
    "name": "芋艿",
    "sex": 0,
    "birthday": "2019-08-24T14:15:22Z",
    "description": "随便",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[Demo03StudentInnerRespVO](#schemademo03studentinnerrespvo)|false|none||管理后台 - 学生 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultDemo03StudentErpRespVO">CommonResultPageResultDemo03StudentErpRespVO</h2>

<a id="schemacommonresultpageresultdemo03studenterprespvo"></a>
<a id="schema_CommonResultPageResultDemo03StudentErpRespVO"></a>
<a id="tocScommonresultpageresultdemo03studenterprespvo"></a>
<a id="tocscommonresultpageresultdemo03studenterprespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 8525,
        "name": "芋艿",
        "sex": 0,
        "birthday": "2019-08-24T14:15:22Z",
        "description": "随便",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultDemo03StudentErpRespVO](#schemapageresultdemo03studenterprespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_Demo03StudentErpRespVO">Demo03StudentErpRespVO</h2>

<a id="schemademo03studenterprespvo"></a>
<a id="schema_Demo03StudentErpRespVO"></a>
<a id="tocSdemo03studenterprespvo"></a>
<a id="tocsdemo03studenterprespvo"></a>

```json
{
  "id": 8525,
  "name": "芋艿",
  "sex": 0,
  "birthday": "2019-08-24T14:15:22Z",
  "description": "随便",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 学生 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|sex|integer(int32)|true|none||性别|
|birthday|string(date-time)|true|none||出生日期|
|description|string|true|none||简介|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultDemo03StudentErpRespVO">PageResultDemo03StudentErpRespVO</h2>

<a id="schemapageresultdemo03studenterprespvo"></a>
<a id="schema_PageResultDemo03StudentErpRespVO"></a>
<a id="tocSpageresultdemo03studenterprespvo"></a>
<a id="tocspageresultdemo03studenterprespvo"></a>

```json
{
  "list": [
    {
      "id": 8525,
      "name": "芋艿",
      "sex": 0,
      "birthday": "2019-08-24T14:15:22Z",
      "description": "随便",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[Demo03StudentErpRespVO](#schemademo03studenterprespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultDemo03StudentErpRespVO">CommonResultDemo03StudentErpRespVO</h2>

<a id="schemacommonresultdemo03studenterprespvo"></a>
<a id="schema_CommonResultDemo03StudentErpRespVO"></a>
<a id="tocScommonresultdemo03studenterprespvo"></a>
<a id="tocscommonresultdemo03studenterprespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 8525,
    "name": "芋艿",
    "sex": 0,
    "birthday": "2019-08-24T14:15:22Z",
    "description": "随便",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[Demo03StudentErpRespVO](#schemademo03studenterprespvo)|false|none||管理后台 - 学生 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultDemo03GradeDO">CommonResultPageResultDemo03GradeDO</h2>

<a id="schemacommonresultpageresultdemo03gradedo"></a>
<a id="schema_CommonResultPageResultDemo03GradeDO"></a>
<a id="tocScommonresultpageresultdemo03gradedo"></a>
<a id="tocscommonresultpageresultdemo03gradedo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "createTime": "2019-08-24T14:15:22Z",
        "updateTime": "2019-08-24T14:15:22Z",
        "creator": "string",
        "updater": "string",
        "deleted": true,
        "id": 0,
        "studentId": 0,
        "name": "string",
        "teacher": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultDemo03GradeDO](#schemapageresultdemo03gradedo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultDemo03GradeDO">PageResultDemo03GradeDO</h2>

<a id="schemapageresultdemo03gradedo"></a>
<a id="schema_PageResultDemo03GradeDO"></a>
<a id="tocSpageresultdemo03gradedo"></a>
<a id="tocspageresultdemo03gradedo"></a>

```json
{
  "list": [
    {
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z",
      "creator": "string",
      "updater": "string",
      "deleted": true,
      "id": 0,
      "studentId": 0,
      "name": "string",
      "teacher": "string"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[Demo03GradeDO](#schemademo03gradedo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultPageResultDemo03CourseDO">CommonResultPageResultDemo03CourseDO</h2>

<a id="schemacommonresultpageresultdemo03coursedo"></a>
<a id="schema_CommonResultPageResultDemo03CourseDO"></a>
<a id="tocScommonresultpageresultdemo03coursedo"></a>
<a id="tocscommonresultpageresultdemo03coursedo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "createTime": "2019-08-24T14:15:22Z",
        "updateTime": "2019-08-24T14:15:22Z",
        "creator": "string",
        "updater": "string",
        "deleted": true,
        "id": 0,
        "studentId": 0,
        "name": "string",
        "score": 0
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultDemo03CourseDO](#schemapageresultdemo03coursedo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultDemo03CourseDO">PageResultDemo03CourseDO</h2>

<a id="schemapageresultdemo03coursedo"></a>
<a id="schema_PageResultDemo03CourseDO"></a>
<a id="tocSpageresultdemo03coursedo"></a>
<a id="tocspageresultdemo03coursedo"></a>

```json
{
  "list": [
    {
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z",
      "creator": "string",
      "updater": "string",
      "deleted": true,
      "id": 0,
      "studentId": 0,
      "name": "string",
      "score": 0
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[Demo03CourseDO](#schemademo03coursedo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultDemo03CourseDO">CommonResultDemo03CourseDO</h2>

<a id="schemacommonresultdemo03coursedo"></a>
<a id="schema_CommonResultDemo03CourseDO"></a>
<a id="tocScommonresultdemo03coursedo"></a>
<a id="tocscommonresultdemo03coursedo"></a>

```json
{
  "code": 0,
  "data": {
    "createTime": "2019-08-24T14:15:22Z",
    "updateTime": "2019-08-24T14:15:22Z",
    "creator": "string",
    "updater": "string",
    "deleted": true,
    "id": 0,
    "studentId": 0,
    "name": "string",
    "score": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[Demo03CourseDO](#schemademo03coursedo)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListDemo02CategoryRespVO">CommonResultListDemo02CategoryRespVO</h2>

<a id="schemacommonresultlistdemo02categoryrespvo"></a>
<a id="schema_CommonResultListDemo02CategoryRespVO"></a>
<a id="tocScommonresultlistdemo02categoryrespvo"></a>
<a id="tocscommonresultlistdemo02categoryrespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 10304,
      "name": "芋艿",
      "parentId": 6080,
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[Demo02CategoryRespVO](#schemademo02categoryrespvo)]|false|none||[管理后台 - 示例分类 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_Demo02CategoryRespVO">Demo02CategoryRespVO</h2>

<a id="schemademo02categoryrespvo"></a>
<a id="schema_Demo02CategoryRespVO"></a>
<a id="tocSdemo02categoryrespvo"></a>
<a id="tocsdemo02categoryrespvo"></a>

```json
{
  "id": 10304,
  "name": "芋艿",
  "parentId": 6080,
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 示例分类 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|parentId|integer(int64)|true|none||父级编号|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultDemo02CategoryRespVO">CommonResultDemo02CategoryRespVO</h2>

<a id="schemacommonresultdemo02categoryrespvo"></a>
<a id="schema_CommonResultDemo02CategoryRespVO"></a>
<a id="tocScommonresultdemo02categoryrespvo"></a>
<a id="tocscommonresultdemo02categoryrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 10304,
    "name": "芋艿",
    "parentId": 6080,
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[Demo02CategoryRespVO](#schemademo02categoryrespvo)|false|none||管理后台 - 示例分类 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultDemo01ContactRespVO">CommonResultPageResultDemo01ContactRespVO</h2>

<a id="schemacommonresultpageresultdemo01contactrespvo"></a>
<a id="schema_CommonResultPageResultDemo01ContactRespVO"></a>
<a id="tocScommonresultpageresultdemo01contactrespvo"></a>
<a id="tocscommonresultpageresultdemo01contactrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 21555,
        "name": "张三",
        "sex": 1,
        "birthday": "2019-08-24T14:15:22Z",
        "description": "你说的对",
        "avatar": "string",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultDemo01ContactRespVO](#schemapageresultdemo01contactrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_Demo01ContactRespVO">Demo01ContactRespVO</h2>

<a id="schemademo01contactrespvo"></a>
<a id="schema_Demo01ContactRespVO"></a>
<a id="tocSdemo01contactrespvo"></a>
<a id="tocsdemo01contactrespvo"></a>

```json
{
  "id": 21555,
  "name": "张三",
  "sex": 1,
  "birthday": "2019-08-24T14:15:22Z",
  "description": "你说的对",
  "avatar": "string",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 示例联系人 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|name|string|true|none||名字|
|sex|integer(int32)|true|none||性别|
|birthday|string(date-time)|true|none||出生年|
|description|string|true|none||简介|
|avatar|string|false|none||头像|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultDemo01ContactRespVO">PageResultDemo01ContactRespVO</h2>

<a id="schemapageresultdemo01contactrespvo"></a>
<a id="schema_PageResultDemo01ContactRespVO"></a>
<a id="tocSpageresultdemo01contactrespvo"></a>
<a id="tocspageresultdemo01contactrespvo"></a>

```json
{
  "list": [
    {
      "id": 21555,
      "name": "张三",
      "sex": 1,
      "birthday": "2019-08-24T14:15:22Z",
      "description": "你说的对",
      "avatar": "string",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[Demo01ContactRespVO](#schemademo01contactrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultDemo01ContactRespVO">CommonResultDemo01ContactRespVO</h2>

<a id="schemacommonresultdemo01contactrespvo"></a>
<a id="schema_CommonResultDemo01ContactRespVO"></a>
<a id="tocScommonresultdemo01contactrespvo"></a>
<a id="tocscommonresultdemo01contactrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 21555,
    "name": "张三",
    "sex": 1,
    "birthday": "2019-08-24T14:15:22Z",
    "description": "你说的对",
    "avatar": "string",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[Demo01ContactRespVO](#schemademo01contactrespvo)|false|none||管理后台 - 示例联系人 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListDataSourceConfigRespVO">CommonResultListDataSourceConfigRespVO</h2>

<a id="schemacommonresultlistdatasourceconfigrespvo"></a>
<a id="schema_CommonResultListDataSourceConfigRespVO"></a>
<a id="tocScommonresultlistdatasourceconfigrespvo"></a>
<a id="tocscommonresultlistdatasourceconfigrespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1024,
      "name": "test",
      "url": "*****************************************",
      "username": "root",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[DataSourceConfigRespVO](#schemadatasourceconfigrespvo)]|false|none||[管理后台 - 数据源配置 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_DataSourceConfigRespVO">DataSourceConfigRespVO</h2>

<a id="schemadatasourceconfigrespvo"></a>
<a id="schema_DataSourceConfigRespVO"></a>
<a id="tocSdatasourceconfigrespvo"></a>
<a id="tocsdatasourceconfigrespvo"></a>

```json
{
  "id": 1024,
  "name": "test",
  "url": "*****************************************",
  "username": "root",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 数据源配置 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||主键编号|
|name|string|true|none||数据源名称|
|url|string|true|none||数据源连接|
|username|string|true|none||用户名|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultDataSourceConfigRespVO">CommonResultDataSourceConfigRespVO</h2>

<a id="schemacommonresultdatasourceconfigrespvo"></a>
<a id="schema_CommonResultDataSourceConfigRespVO"></a>
<a id="tocScommonresultdatasourceconfigrespvo"></a>
<a id="tocscommonresultdatasourceconfigrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "name": "test",
    "url": "*****************************************",
    "username": "root",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[DataSourceConfigRespVO](#schemadatasourceconfigrespvo)|false|none||管理后台 - 数据源配置 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultPageResultConfigRespVO">CommonResultPageResultConfigRespVO</h2>

<a id="schemacommonresultpageresultconfigrespvo"></a>
<a id="schema_CommonResultPageResultConfigRespVO"></a>
<a id="tocScommonresultpageresultconfigrespvo"></a>
<a id="tocscommonresultpageresultconfigrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "category": "biz",
        "name": "数据库名",
        "key": "yunai.db.username",
        "value": 1024,
        "type": 1,
        "visible": true,
        "remark": "备注一下很帅气！",
        "createTime": "时间戳格式"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultConfigRespVO](#schemapageresultconfigrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_ConfigRespVO">ConfigRespVO</h2>

<a id="schemaconfigrespvo"></a>
<a id="schema_ConfigRespVO"></a>
<a id="tocSconfigrespvo"></a>
<a id="tocsconfigrespvo"></a>

```json
{
  "id": 1024,
  "category": "biz",
  "name": "数据库名",
  "key": "yunai.db.username",
  "value": 1024,
  "type": 1,
  "visible": true,
  "remark": "备注一下很帅气！",
  "createTime": "时间戳格式"
}

```

管理后台 - 参数配置信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||参数配置序号|
|category|string|true|none||参数分类|
|name|string|true|none||参数名称|
|key|string|true|none||参数键名|
|value|string|true|none||参数键值|
|type|integer(int32)|true|none||参数类型，参见 SysConfigTypeEnum 枚举|
|visible|boolean|true|none||是否可见|
|remark|string|false|none||备注|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_PageResultConfigRespVO">PageResultConfigRespVO</h2>

<a id="schemapageresultconfigrespvo"></a>
<a id="schema_PageResultConfigRespVO"></a>
<a id="tocSpageresultconfigrespvo"></a>
<a id="tocspageresultconfigrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "category": "biz",
      "name": "数据库名",
      "key": "yunai.db.username",
      "value": 1024,
      "type": 1,
      "visible": true,
      "remark": "备注一下很帅气！",
      "createTime": "时间戳格式"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[ConfigRespVO](#schemaconfigrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultConfigRespVO">CommonResultConfigRespVO</h2>

<a id="schemacommonresultconfigrespvo"></a>
<a id="schema_CommonResultConfigRespVO"></a>
<a id="tocScommonresultconfigrespvo"></a>
<a id="tocscommonresultconfigrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1024,
    "category": "biz",
    "name": "数据库名",
    "key": "yunai.db.username",
    "value": 1024,
    "type": 1,
    "visible": true,
    "remark": "备注一下很帅气！",
    "createTime": "时间戳格式"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[ConfigRespVO](#schemaconfigrespvo)|false|none||管理后台 - 参数配置信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CodegenTableRespVO">CodegenTableRespVO</h2>

<a id="schemacodegentablerespvo"></a>
<a id="schema_CodegenTableRespVO"></a>
<a id="tocScodegentablerespvo"></a>
<a id="tocscodegentablerespvo"></a>

```json
{
  "id": 1,
  "scene": 1,
  "tableName": "yudao",
  "tableComment": "芋道",
  "remark": "我是备注",
  "moduleName": "system",
  "businessName": "codegen",
  "className": "CodegenTable",
  "classComment": "代码生成器的表定义",
  "author": "芋道源码",
  "templateType": 1,
  "frontType": 20,
  "parentMenuId": 1024,
  "masterTableId": 2048,
  "subJoinColumnId": 4096,
  "subJoinMany": 4096,
  "treeParentColumnId": 8192,
  "treeNameColumnId": 16384,
  "dataSourceConfigId": 1024,
  "createTime": "2019-08-24T14:15:22Z",
  "updateTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 代码生成表定义 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|scene|integer(int32)|true|none||生成场景，参见 CodegenSceneEnum 枚举|
|tableName|string|true|none||表名称|
|tableComment|string|true|none||表描述|
|remark|string|false|none||备注|
|moduleName|string|true|none||模块名|
|businessName|string|true|none||业务名|
|className|string|true|none||类名称|
|classComment|string|true|none||类描述|
|author|string|true|none||作者|
|templateType|integer(int32)|true|none||模板类型，参见 CodegenTemplateTypeEnum 枚举|
|frontType|integer(int32)|true|none||前端类型，参见 CodegenFrontTypeEnum 枚举|
|parentMenuId|integer(int64)|false|none||父菜单编号|
|masterTableId|integer(int64)|false|none||主表的编号|
|subJoinColumnId|integer(int64)|false|none||子表关联主表的字段编号|
|subJoinMany|boolean|false|none||主表与子表是否一对多|
|treeParentColumnId|integer(int64)|false|none||树表的父字段编号|
|treeNameColumnId|integer(int64)|false|none||树表的名字字段编号|
|dataSourceConfigId|integer(int32)|true|none||主键编号|
|createTime|string(date-time)|true|none||创建时间|
|updateTime|string(date-time)|true|none||更新时间|

<h2 id="tocS_CommonResultPageResultCodegenTableRespVO">CommonResultPageResultCodegenTableRespVO</h2>

<a id="schemacommonresultpageresultcodegentablerespvo"></a>
<a id="schema_CommonResultPageResultCodegenTableRespVO"></a>
<a id="tocScommonresultpageresultcodegentablerespvo"></a>
<a id="tocscommonresultpageresultcodegentablerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "scene": 1,
        "tableName": "yudao",
        "tableComment": "芋道",
        "remark": "我是备注",
        "moduleName": "system",
        "businessName": "codegen",
        "className": "CodegenTable",
        "classComment": "代码生成器的表定义",
        "author": "芋道源码",
        "templateType": 1,
        "frontType": 20,
        "parentMenuId": 1024,
        "masterTableId": 2048,
        "subJoinColumnId": 4096,
        "subJoinMany": 4096,
        "treeParentColumnId": 8192,
        "treeNameColumnId": 16384,
        "dataSourceConfigId": 1024,
        "createTime": "2019-08-24T14:15:22Z",
        "updateTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultCodegenTableRespVO](#schemapageresultcodegentablerespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultCodegenTableRespVO">PageResultCodegenTableRespVO</h2>

<a id="schemapageresultcodegentablerespvo"></a>
<a id="schema_PageResultCodegenTableRespVO"></a>
<a id="tocSpageresultcodegentablerespvo"></a>
<a id="tocspageresultcodegentablerespvo"></a>

```json
{
  "list": [
    {
      "id": 1,
      "scene": 1,
      "tableName": "yudao",
      "tableComment": "芋道",
      "remark": "我是备注",
      "moduleName": "system",
      "businessName": "codegen",
      "className": "CodegenTable",
      "classComment": "代码生成器的表定义",
      "author": "芋道源码",
      "templateType": 1,
      "frontType": 20,
      "parentMenuId": 1024,
      "masterTableId": 2048,
      "subJoinColumnId": 4096,
      "subJoinMany": 4096,
      "treeParentColumnId": 8192,
      "treeNameColumnId": 16384,
      "dataSourceConfigId": 1024,
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[CodegenTableRespVO](#schemacodegentablerespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultListCodegenTableRespVO">CommonResultListCodegenTableRespVO</h2>

<a id="schemacommonresultlistcodegentablerespvo"></a>
<a id="schema_CommonResultListCodegenTableRespVO"></a>
<a id="tocScommonresultlistcodegentablerespvo"></a>
<a id="tocscommonresultlistcodegentablerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "scene": 1,
      "tableName": "yudao",
      "tableComment": "芋道",
      "remark": "我是备注",
      "moduleName": "system",
      "businessName": "codegen",
      "className": "CodegenTable",
      "classComment": "代码生成器的表定义",
      "author": "芋道源码",
      "templateType": 1,
      "frontType": 20,
      "parentMenuId": 1024,
      "masterTableId": 2048,
      "subJoinColumnId": 4096,
      "subJoinMany": 4096,
      "treeParentColumnId": 8192,
      "treeNameColumnId": 16384,
      "dataSourceConfigId": 1024,
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[CodegenTableRespVO](#schemacodegentablerespvo)]|false|none||[管理后台 - 代码生成表定义 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_CodegenPreviewRespVO">CodegenPreviewRespVO</h2>

<a id="schemacodegenpreviewrespvo"></a>
<a id="schema_CodegenPreviewRespVO"></a>
<a id="tocScodegenpreviewrespvo"></a>
<a id="tocscodegenpreviewrespvo"></a>

```json
{
  "filePath": "java/cn/iocoder/yudao/adminserver/modules/system/controller/test/SysTestDemoController.java",
  "code": "Hello World"
}

```

管理后台 - 代码生成预览 Response VO，注意，每个文件都是一个该对象

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|filePath|string|true|none||文件路径|
|code|string|true|none||代码|

<h2 id="tocS_CommonResultListCodegenPreviewRespVO">CommonResultListCodegenPreviewRespVO</h2>

<a id="schemacommonresultlistcodegenpreviewrespvo"></a>
<a id="schema_CommonResultListCodegenPreviewRespVO"></a>
<a id="tocScommonresultlistcodegenpreviewrespvo"></a>
<a id="tocscommonresultlistcodegenpreviewrespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "filePath": "java/cn/iocoder/yudao/adminserver/modules/system/controller/test/SysTestDemoController.java",
      "code": "Hello World"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[CodegenPreviewRespVO](#schemacodegenpreviewrespvo)]|false|none||[管理后台 - 代码生成预览 Response VO，注意，每个文件都是一个该对象]|
|msg|string|false|none||none|

<h2 id="tocS_CodegenColumnRespVO">CodegenColumnRespVO</h2>

<a id="schemacodegencolumnrespvo"></a>
<a id="schema_CodegenColumnRespVO"></a>
<a id="tocScodegencolumnrespvo"></a>
<a id="tocscodegencolumnrespvo"></a>

```json
{
  "id": 1,
  "tableId": 1,
  "columnName": "user_age",
  "dataType": "int(11)",
  "columnComment": "年龄",
  "nullable": true,
  "primaryKey": false,
  "ordinalPosition": 10,
  "javaType": "userAge",
  "javaField": "Integer",
  "dictType": "sys_gender",
  "example": 1024,
  "createOperation": true,
  "updateOperation": false,
  "listOperation": true,
  "listOperationCondition": "LIKE",
  "listOperationResult": true,
  "htmlType": "input",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 代码生成字段定义 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|tableId|integer(int64)|true|none||表编号|
|columnName|string|true|none||字段名|
|dataType|string|true|none||字段类型|
|columnComment|string|true|none||字段描述|
|nullable|boolean|true|none||是否允许为空|
|primaryKey|boolean|true|none||是否主键|
|ordinalPosition|integer(int32)|true|none||排序|
|javaType|string|true|none||Java 属性类型|
|javaField|string|true|none||Java 属性名|
|dictType|string|false|none||字典类型|
|example|string|false|none||数据示例|
|createOperation|boolean|true|none||是否为 Create 创建操作的字段|
|updateOperation|boolean|true|none||是否为 Update 更新操作的字段|
|listOperation|boolean|true|none||是否为 List 查询操作的字段|
|listOperationCondition|string|true|none||List 查询操作的条件类型，参见 CodegenColumnListConditionEnum 枚举|
|listOperationResult|boolean|true|none||是否为 List 查询操作的返回字段|
|htmlType|string|true|none||显示类型|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CodegenDetailRespVO">CodegenDetailRespVO</h2>

<a id="schemacodegendetailrespvo"></a>
<a id="schema_CodegenDetailRespVO"></a>
<a id="tocScodegendetailrespvo"></a>
<a id="tocscodegendetailrespvo"></a>

```json
{
  "table": {
    "id": 1,
    "scene": 1,
    "tableName": "yudao",
    "tableComment": "芋道",
    "remark": "我是备注",
    "moduleName": "system",
    "businessName": "codegen",
    "className": "CodegenTable",
    "classComment": "代码生成器的表定义",
    "author": "芋道源码",
    "templateType": 1,
    "frontType": 20,
    "parentMenuId": 1024,
    "masterTableId": 2048,
    "subJoinColumnId": 4096,
    "subJoinMany": 4096,
    "treeParentColumnId": 8192,
    "treeNameColumnId": 16384,
    "dataSourceConfigId": 1024,
    "createTime": "2019-08-24T14:15:22Z",
    "updateTime": "2019-08-24T14:15:22Z"
  },
  "columns": [
    {
      "id": 1,
      "tableId": 1,
      "columnName": "user_age",
      "dataType": "int(11)",
      "columnComment": "年龄",
      "nullable": true,
      "primaryKey": false,
      "ordinalPosition": 10,
      "javaType": "userAge",
      "javaField": "Integer",
      "dictType": "sys_gender",
      "example": 1024,
      "createOperation": true,
      "updateOperation": false,
      "listOperation": true,
      "listOperationCondition": "LIKE",
      "listOperationResult": true,
      "htmlType": "input",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ]
}

```

管理后台 - 代码生成表和字段的明细 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|table|[CodegenTableRespVO](#schemacodegentablerespvo)|false|none||表定义|
|columns|[[CodegenColumnRespVO](#schemacodegencolumnrespvo)]|false|none||字段定义|

<h2 id="tocS_CommonResultCodegenDetailRespVO">CommonResultCodegenDetailRespVO</h2>

<a id="schemacommonresultcodegendetailrespvo"></a>
<a id="schema_CommonResultCodegenDetailRespVO"></a>
<a id="tocScommonresultcodegendetailrespvo"></a>
<a id="tocscommonresultcodegendetailrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "table": {
      "id": 1,
      "scene": 1,
      "tableName": "yudao",
      "tableComment": "芋道",
      "remark": "我是备注",
      "moduleName": "system",
      "businessName": "codegen",
      "className": "CodegenTable",
      "classComment": "代码生成器的表定义",
      "author": "芋道源码",
      "templateType": 1,
      "frontType": 20,
      "parentMenuId": 1024,
      "masterTableId": 2048,
      "subJoinColumnId": 4096,
      "subJoinMany": 4096,
      "treeParentColumnId": 8192,
      "treeNameColumnId": 16384,
      "dataSourceConfigId": 1024,
      "createTime": "2019-08-24T14:15:22Z",
      "updateTime": "2019-08-24T14:15:22Z"
    },
    "columns": [
      {
        "id": 1,
        "tableId": 1,
        "columnName": "user_age",
        "dataType": "int(11)",
        "columnComment": "年龄",
        "nullable": true,
        "primaryKey": false,
        "ordinalPosition": 10,
        "javaType": "userAge",
        "javaField": "Integer",
        "dictType": "sys_gender",
        "example": 1024,
        "createOperation": true,
        "updateOperation": false,
        "listOperation": true,
        "listOperationCondition": "LIKE",
        "listOperationResult": true,
        "htmlType": "input",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ]
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[CodegenDetailRespVO](#schemacodegendetailrespvo)|false|none||管理后台 - 代码生成表和字段的明细 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListDatabaseTableRespVO">CommonResultListDatabaseTableRespVO</h2>

<a id="schemacommonresultlistdatabasetablerespvo"></a>
<a id="schema_CommonResultListDatabaseTableRespVO"></a>
<a id="tocScommonresultlistdatabasetablerespvo"></a>
<a id="tocscommonresultlistdatabasetablerespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "name": "yuanma",
      "comment": "芋道源码"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[DatabaseTableRespVO](#schemadatabasetablerespvo)]|false|none||[管理后台 - 数据库的表定义 Response VO]|
|msg|string|false|none||none|

<h2 id="tocS_DatabaseTableRespVO">DatabaseTableRespVO</h2>

<a id="schemadatabasetablerespvo"></a>
<a id="schema_DatabaseTableRespVO"></a>
<a id="tocSdatabasetablerespvo"></a>
<a id="tocsdatabasetablerespvo"></a>

```json
{
  "name": "yuanma",
  "comment": "芋道源码"
}

```

管理后台 - 数据库的表定义 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|name|string|true|none||表名称|
|comment|string|true|none||表描述|

<h2 id="tocS_ApiErrorLogRespVO">ApiErrorLogRespVO</h2>

<a id="schemaapierrorlogrespvo"></a>
<a id="schema_ApiErrorLogRespVO"></a>
<a id="tocSapierrorlogrespvo"></a>
<a id="tocsapierrorlogrespvo"></a>

```json
{
  "id": 1024,
  "traceId": "66600cb6-7852-11eb-9439-0242ac130002",
  "userId": 666,
  "userType": 1,
  "applicationName": "dashboard",
  "requestMethod": "GET",
  "requestUrl": "/xx/yy",
  "requestParams": "string",
  "userIp": "127.0.0.1",
  "userAgent": "Mozilla/5.0",
  "exceptionTime": "2019-08-24T14:15:22Z",
  "exceptionName": "string",
  "exceptionMessage": "string",
  "exceptionRootCauseMessage": "string",
  "exceptionStackTrace": "string",
  "exceptionClassName": "string",
  "exceptionFileName": "string",
  "exceptionMethodName": "string",
  "exceptionLineNumber": 0,
  "processStatus": 0,
  "processTime": "2019-08-24T14:15:22Z",
  "processUserId": 233,
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - API 错误日志 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||编号|
|traceId|string|true|none||链路追踪编号|
|userId|integer(int64)|true|none||用户编号|
|userType|integer(int32)|true|none||用户类型|
|applicationName|string|true|none||应用名|
|requestMethod|string|true|none||请求方法名|
|requestUrl|string|true|none||请求地址|
|requestParams|string|true|none||请求参数|
|userIp|string|true|none||用户 IP|
|userAgent|string|true|none||浏览器 UA|
|exceptionTime|string(date-time)|true|none||异常发生时间|
|exceptionName|string|true|none||异常名|
|exceptionMessage|string|true|none||异常导致的消息|
|exceptionRootCauseMessage|string|true|none||异常导致的根消息|
|exceptionStackTrace|string|true|none||异常的栈轨迹|
|exceptionClassName|string|true|none||异常发生的类全名|
|exceptionFileName|string|true|none||异常发生的类文件|
|exceptionMethodName|string|true|none||异常发生的方法名|
|exceptionLineNumber|integer(int32)|true|none||异常发生的方法所在行|
|processStatus|integer(int32)|true|none||处理状态|
|processTime|string(date-time)|true|none||处理时间|
|processUserId|integer(int32)|false|none||处理用户编号|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultPageResultApiErrorLogRespVO">CommonResultPageResultApiErrorLogRespVO</h2>

<a id="schemacommonresultpageresultapierrorlogrespvo"></a>
<a id="schema_CommonResultPageResultApiErrorLogRespVO"></a>
<a id="tocScommonresultpageresultapierrorlogrespvo"></a>
<a id="tocscommonresultpageresultapierrorlogrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "traceId": "66600cb6-7852-11eb-9439-0242ac130002",
        "userId": 666,
        "userType": 1,
        "applicationName": "dashboard",
        "requestMethod": "GET",
        "requestUrl": "/xx/yy",
        "requestParams": "string",
        "userIp": "127.0.0.1",
        "userAgent": "Mozilla/5.0",
        "exceptionTime": "2019-08-24T14:15:22Z",
        "exceptionName": "string",
        "exceptionMessage": "string",
        "exceptionRootCauseMessage": "string",
        "exceptionStackTrace": "string",
        "exceptionClassName": "string",
        "exceptionFileName": "string",
        "exceptionMethodName": "string",
        "exceptionLineNumber": 0,
        "processStatus": 0,
        "processTime": "2019-08-24T14:15:22Z",
        "processUserId": 233,
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultApiErrorLogRespVO](#schemapageresultapierrorlogrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultApiErrorLogRespVO">PageResultApiErrorLogRespVO</h2>

<a id="schemapageresultapierrorlogrespvo"></a>
<a id="schema_PageResultApiErrorLogRespVO"></a>
<a id="tocSpageresultapierrorlogrespvo"></a>
<a id="tocspageresultapierrorlogrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "traceId": "66600cb6-7852-11eb-9439-0242ac130002",
      "userId": 666,
      "userType": 1,
      "applicationName": "dashboard",
      "requestMethod": "GET",
      "requestUrl": "/xx/yy",
      "requestParams": "string",
      "userIp": "127.0.0.1",
      "userAgent": "Mozilla/5.0",
      "exceptionTime": "2019-08-24T14:15:22Z",
      "exceptionName": "string",
      "exceptionMessage": "string",
      "exceptionRootCauseMessage": "string",
      "exceptionStackTrace": "string",
      "exceptionClassName": "string",
      "exceptionFileName": "string",
      "exceptionMethodName": "string",
      "exceptionLineNumber": 0,
      "processStatus": 0,
      "processTime": "2019-08-24T14:15:22Z",
      "processUserId": 233,
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[ApiErrorLogRespVO](#schemaapierrorlogrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_ApiAccessLogRespVO">ApiAccessLogRespVO</h2>

<a id="schemaapiaccesslogrespvo"></a>
<a id="schema_ApiAccessLogRespVO"></a>
<a id="tocSapiaccesslogrespvo"></a>
<a id="tocsapiaccesslogrespvo"></a>

```json
{
  "id": 1024,
  "traceId": "66600cb6-7852-11eb-9439-0242ac130002",
  "userId": 666,
  "userType": 2,
  "applicationName": "dashboard",
  "requestMethod": "GET",
  "requestUrl": "/xxx/yyy",
  "requestParams": "string",
  "responseBody": "string",
  "userIp": "127.0.0.1",
  "userAgent": "Mozilla/5.0",
  "operateModule": "商品模块",
  "operateName": "创建商品",
  "operateType": 1,
  "beginTime": "2019-08-24T14:15:22Z",
  "endTime": "2019-08-24T14:15:22Z",
  "duration": 100,
  "resultCode": 0,
  "resultMsg": "芋道源码，牛逼！",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - API 访问日志 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||日志主键|
|traceId|string|true|none||链路追踪编号|
|userId|integer(int64)|true|none||用户编号|
|userType|integer(int32)|true|none||用户类型，参见 UserTypeEnum 枚举|
|applicationName|string|true|none||应用名|
|requestMethod|string|true|none||请求方法名|
|requestUrl|string|true|none||请求地址|
|requestParams|string|false|none||请求参数|
|responseBody|string|false|none||响应结果|
|userIp|string|true|none||用户 IP|
|userAgent|string|true|none||浏览器 UA|
|operateModule|string|true|none||操作模块|
|operateName|string|true|none||操作名|
|operateType|integer(int32)|true|none||操作分类|
|beginTime|string(date-time)|true|none||开始请求时间|
|endTime|string(date-time)|true|none||结束请求时间|
|duration|integer(int32)|true|none||执行时长|
|resultCode|integer(int32)|true|none||结果码|
|resultMsg|string|false|none||结果提示|
|createTime|string(date-time)|true|none||创建时间|

<h2 id="tocS_CommonResultPageResultApiAccessLogRespVO">CommonResultPageResultApiAccessLogRespVO</h2>

<a id="schemacommonresultpageresultapiaccesslogrespvo"></a>
<a id="schema_CommonResultPageResultApiAccessLogRespVO"></a>
<a id="tocScommonresultpageresultapiaccesslogrespvo"></a>
<a id="tocscommonresultpageresultapiaccesslogrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "traceId": "66600cb6-7852-11eb-9439-0242ac130002",
        "userId": 666,
        "userType": 2,
        "applicationName": "dashboard",
        "requestMethod": "GET",
        "requestUrl": "/xxx/yyy",
        "requestParams": "string",
        "responseBody": "string",
        "userIp": "127.0.0.1",
        "userAgent": "Mozilla/5.0",
        "operateModule": "商品模块",
        "operateName": "创建商品",
        "operateType": 1,
        "beginTime": "2019-08-24T14:15:22Z",
        "endTime": "2019-08-24T14:15:22Z",
        "duration": 100,
        "resultCode": 0,
        "resultMsg": "芋道源码，牛逼！",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultApiAccessLogRespVO](#schemapageresultapiaccesslogrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultApiAccessLogRespVO">PageResultApiAccessLogRespVO</h2>

<a id="schemapageresultapiaccesslogrespvo"></a>
<a id="schema_PageResultApiAccessLogRespVO"></a>
<a id="tocSpageresultapiaccesslogrespvo"></a>
<a id="tocspageresultapiaccesslogrespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "traceId": "66600cb6-7852-11eb-9439-0242ac130002",
      "userId": 666,
      "userType": 2,
      "applicationName": "dashboard",
      "requestMethod": "GET",
      "requestUrl": "/xxx/yyy",
      "requestParams": "string",
      "responseBody": "string",
      "userIp": "127.0.0.1",
      "userAgent": "Mozilla/5.0",
      "operateModule": "商品模块",
      "operateName": "创建商品",
      "operateType": 1,
      "beginTime": "2019-08-24T14:15:22Z",
      "endTime": "2019-08-24T14:15:22Z",
      "duration": 100,
      "resultCode": 0,
      "resultMsg": "芋道源码，牛逼！",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[ApiAccessLogRespVO](#schemaapiaccesslogrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_SocialUserUnbindReqVO">SocialUserUnbindReqVO</h2>

<a id="schemasocialuserunbindreqvo"></a>
<a id="schema_SocialUserUnbindReqVO"></a>
<a id="tocSsocialuserunbindreqvo"></a>
<a id="tocssocialuserunbindreqvo"></a>

```json
{
  "type": 10,
  "openid": "IPRmJ0wvBptiPIlGEZiPewGwiEiE"
}

```

管理后台 - 取消社交绑定 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|integer(int32)|true|none||社交平台的类型，参见 UserSocialTypeEnum 枚举值|
|openid|string|true|none||社交用户的 openid|

<h2 id="tocS_CommonResultBoolean">CommonResultBoolean</h2>

<a id="schemacommonresultboolean"></a>
<a id="schema_CommonResultBoolean"></a>
<a id="tocScommonresultboolean"></a>
<a id="tocscommonresultboolean"></a>

```json
{
  "code": 0,
  "data": true,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|boolean|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_AiModelUpdateReqVO">AiModelUpdateReqVO</h2>

<a id="schemaaimodelupdatereqvo"></a>
<a id="schema_AiModelUpdateReqVO"></a>
<a id="tocSaimodelupdatereqvo"></a>
<a id="tocsaimodelupdatereqvo"></a>

```json
{
  "id": 24150,
  "modelName": "张三",
  "modelFileId": "14429",
  "modelVideoFileId": "19647",
  "coverImageFileId": 14429,
  "status": 0,
  "createTime": "string",
  "creator": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "tenantId": 17682
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||ID|
|modelName|string|true|none||模型名称|
|modelFileId|string|true|none||关联的模型文件ID|
|modelVideoFileId|string|false|none||模型宣传视频文件id|
|coverImageFileId|integer(int64)|false|none||模型封面文件ID|
|status|integer|false|none||启用状态：0-启用，1-停用|
|createTime|string|false|none||创建时间|
|creator|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updater|string|false|none||更新人|
|deleted|boolean|false|none||0代表存在 1代表删除|
|tenantId|integer(int64)|false|none||租户编号|

<h2 id="tocS_CommonResult">CommonResult</h2>

<a id="schemacommonresult"></a>
<a id="schema_CommonResult"></a>
<a id="tocScommonresult"></a>
<a id="tocscommonresult"></a>

```json
{
  "code": 0,
  "data": {},
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|object|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_AlarmNotifyDTO">AlarmNotifyDTO</h2>

<a id="schemaalarmnotifydto"></a>
<a id="schema_AlarmNotifyDTO"></a>
<a id="tocSalarmnotifydto"></a>
<a id="tocsalarmnotifydto"></a>

```json
{
  "taskUniqueId": "27440",
  "fileUniqueId": "27440",
  "jsonFileUrl": "27440",
  "alarmImgFileUrl": "27440"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|taskUniqueId|string|false|none||任务唯一id|
|fileUniqueId|string|false|none||任务关联的文件id|
|jsonFileUrl|string|false|none||算法结果json文件地址|
|alarmImgFileUrl|string|false|none||算法结果图片文件地址|

<h2 id="tocS_S3PartDTO">S3PartDTO</h2>

<a id="schemas3partdto"></a>
<a id="schema_S3PartDTO"></a>
<a id="tocSs3partdto"></a>
<a id="tocss3partdto"></a>

```json
{
  "partNumber": 0,
  "eTag": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|partNumber|integer|false|none||分片编号|
|eTag|string|false|none||分片的ETag值|

<h2 id="tocS_key">key</h2>

<a id="schemakey"></a>
<a id="schema_key"></a>
<a id="tocSkey"></a>
<a id="tocskey"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_RedisUploadPartDTO">RedisUploadPartDTO</h2>

<a id="schemaredisuploadpartdto"></a>
<a id="schema_RedisUploadPartDTO"></a>
<a id="tocSredisuploadpartdto"></a>
<a id="tocsredisuploadpartdto"></a>

```json
{
  "objectKey": "string",
  "uploadId": "string",
  "partNumber": 0,
  "multipartFile": "string",
  "partCount": 0,
  "s3PartDTOS": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|objectKey|string|false|none||none|
|uploadId|string|false|none||none|
|partNumber|integer|false|none||none|
|multipartFile|string|false|none||none|
|partCount|integer|false|none||none|
|s3PartDTOS|[[S3PartDTO](#schemas3partdto)]|false|none||none|

<h2 id="tocS_MapObject">MapObject</h2>

<a id="schemamapobject"></a>
<a id="schema_MapObject"></a>
<a id="tocSmapobject"></a>
<a id="tocsmapobject"></a>

```json
{
  "key": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|[key](#schemakey)|false|none||none|

<h2 id="tocS_Part">Part</h2>

<a id="schemapart"></a>
<a id="schema_Part"></a>
<a id="tocSpart"></a>
<a id="tocspart"></a>

```json
{
  "partNumber": 0,
  "lastModified": {
    "seconds": 0,
    "nanos": 0
  },
  "eTag": "string",
  "size": 0,
  "checksumCRC32": "string",
  "checksumCRC32C": "string",
  "checksumCRC64NVME": "string",
  "checksumSHA1": "string",
  "checksumSHA256": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|partNumber|integer|false|none||none|
|lastModified|[Instant](#schemainstant)|false|none||none|
|eTag|string|false|none||none|
|size|integer(int64)|false|none||none|
|checksumCRC32|string|false|none||none|
|checksumCRC32C|string|false|none||none|
|checksumCRC64NVME|string|false|none||none|
|checksumSHA1|string|false|none||none|
|checksumSHA256|string|false|none||none|

<h2 id="tocS_FileConfigSaveReqVO">FileConfigSaveReqVO</h2>

<a id="schemafileconfigsavereqvo"></a>
<a id="schema_FileConfigSaveReqVO"></a>
<a id="tocSfileconfigsavereqvo"></a>
<a id="tocsfileconfigsavereqvo"></a>

```json
{
  "id": 1,
  "name": "S3 - 阿里云",
  "storage": 1,
  "config": {
    "key": {}
  },
  "remark": "我是备注"
}

```

管理后台 - 文件配置创建/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||编号|
|name|string|true|none||配置名|
|storage|integer|true|none||存储器，参见 FileStorageEnum 枚举类|
|config|[MapObject](#schemamapobject)|true|none||存储配置,配置是动态参数，所以使用 Map 接收|
|remark|string|false|none||备注|

<h2 id="tocS_TaskSaveReqVO">TaskSaveReqVO</h2>

<a id="schematasksavereqvo"></a>
<a id="schema_TaskSaveReqVO"></a>
<a id="tocStasksavereqvo"></a>
<a id="tocstasksavereqvo"></a>

```json
{
  "id": 27454,
  "name": "任务001",
  "taskType": 0,
  "taskDataId": 29021,
  "taskDataName": 29021,
  "status": 1,
  "aiModelId": "5308",
  "aiModelName": "模型名称",
  "alarmNoticeUserId": "2717",
  "reason": "不好",
  "creator": "string",
  "createTime": "string",
  "updater": "string",
  "deleted": true,
  "updateTime": "string",
  "tenantId": 9293
}

```

管理后台 - ai识别任务新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|name|string|false|none||识别报告名称|
|taskType|integer|false|none||任务类型 0数据集；1视频流|
|taskDataId|integer(int64)|false|none||数据集或者视频流的id，类型根据task_type判断|
|taskDataName|integer(int64)|false|none||数据集或者视频流的名称，类型根据task_type判断|
|status|integer|false|none||识别状态|
|aiModelId|string|false|none||识别模型id|
|aiModelName|string|false|none||识别模型名称|
|alarmNoticeUserId|string|false|none||告警通知人员id，多个用逗号隔开|
|reason|string|false|none||识别失败原因|
|creator|string|false|none||创建人|
|createTime|string|false|none||创建时间|
|updater|string|false|none||更新人|
|deleted|boolean|false|none||0代表存在 2代表删除|
|updateTime|string|false|none||更新时间|
|tenantId|integer(int64)|false|none||租户编号|

<h2 id="tocS_TaskFilesSaveReqVO">TaskFilesSaveReqVO</h2>

<a id="schemataskfilessavereqvo"></a>
<a id="schema_TaskFilesSaveReqVO"></a>
<a id="tocStaskfilessavereqvo"></a>
<a id="tocstaskfilessavereqvo"></a>

```json
{
  "id": 32320,
  "taskId": 2269,
  "taskFileId": 9678,
  "type": 2,
  "originalFileUrl": "https://www.iocoder.cn",
  "processedFileUrl": "https://www.iocoder.cn",
  "processedFileId": "24399",
  "hasAlarm": 0,
  "status": 1,
  "creator": "string",
  "resultJsonFileId": "12959",
  "updater": "string",
  "resultJsonFileUrl": "https://www.iocoder.cn",
  "deleted": true,
  "createTime": "string",
  "tenantId": 26007,
  "updateTime": "string"
}

```

管理后台 - 任务文件：视频、图片新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|taskId|integer(int64)|false|none||识别任务id|
|taskFileId|integer(int64)|false|none||关联的识别文件id|
|type|integer|false|none||文件类型：0-图片，1-视频|
|originalFileUrl|string|false|none||被识别的文件url|
|processedFileUrl|string|false|none||识别后的文件url|
|processedFileId|string|false|none||识别后的文件id|
|hasAlarm|integer|false|none||是否有隐患：0-否，1-是|
|status|integer|false|none||识别状态：0-识别中，10-识别完成，20-识别失败|
|creator|string|false|none||创建人|
|resultJsonFileId|string|false|none||识别结果json文件id|
|updater|string|false|none||更新人|
|resultJsonFileUrl|string|false|none||识别结果json文件url|
|deleted|boolean|false|none||0代表存在 2代表删除|
|createTime|string|false|none||创建时间|
|tenantId|integer(int64)|false|none||租户编号|
|updateTime|string|false|none||更新时间|

<h2 id="tocS_ResourceDeviceSaveReqVO">ResourceDeviceSaveReqVO</h2>

<a id="schemaresourcedevicesavereqvo"></a>
<a id="schema_ResourceDeviceSaveReqVO"></a>
<a id="tocSresourcedevicesavereqvo"></a>
<a id="tocsresourcedevicesavereqvo"></a>

```json
{
  "id": 28557,
  "deviceCode": "string",
  "deviceName": "张三",
  "ip": "string",
  "port": 0,
  "description": "你猜",
  "username": "李四",
  "password": "string",
  "channel": "string",
  "liveUrl": "https://www.iocoder.cn",
  "status": 1,
  "createTime": "string",
  "creator": 0,
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "deptId": 31191,
  "tenantId": 30909
}

```

管理后台 - 资源设备新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|deviceCode|string|false|none||设备编号|
|deviceName|string|false|none||设备名称|
|ip|string|false|none||ip地址|
|port|integer|false|none||端口|
|description|string|false|none||描述|
|username|string|false|none||设备账号|
|password|string|false|none||密码|
|channel|string|false|none||视频流通道|
|liveUrl|string|false|none||视频链接|
|status|integer|false|none||0-离线，10-在线|
|createTime|string|false|none||创建时间|
|creator|integer(int64)|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updater|string|false|none||更新人|
|deleted|boolean|false|none||删除标志（0代表存在 2代表删除）|
|deptId|integer(int64)|false|none||所属部门|
|tenantId|integer(int64)|false|none||租户编号|

<h2 id="tocS_DatasetSaveReqVO">DatasetSaveReqVO</h2>

<a id="schemadatasetsavereqvo"></a>
<a id="schema_DatasetSaveReqVO"></a>
<a id="tocSdatasetsavereqvo"></a>
<a id="tocsdatasetsavereqvo"></a>

```json
{
  "id": 11095,
  "name": "李四",
  "type": 0,
  "coverImageFileId": 14429,
  "status": 1,
  "markFlag": 0,
  "creator": "string",
  "updater": "string",
  "deleted": "string"
}

```

管理后台 - 数据集新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|name|string|false|none||数据集名称|
|type|integer|false|none||数据集类型|
|coverImageFileId|integer(int64)|false|none||数据集封面文件ID|
|status|integer|false|none||上传状态|
|markFlag|integer|false|none||是否标注 0 否；1是|
|creator|string|false|none||创建人|
|updater|string|false|none||更新人|
|deleted|string|false|none||0代表存在 2代表删除|

<h2 id="tocS_DatasetFilesSaveReqVO">DatasetFilesSaveReqVO</h2>

<a id="schemadatasetfilessavereqvo"></a>
<a id="schema_DatasetFilesSaveReqVO"></a>
<a id="tocSdatasetfilessavereqvo"></a>
<a id="tocsdatasetfilessavereqvo"></a>

```json
{
  "datasetId": 21267,
  "fileVos": [
    {
      "fileName": "9672",
      "fileType": 0,
      "fileId": 123423423423445
    }
  ]
}

```

管理后台 - 数据集文件信息新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|datasetId|integer(int64)|false|none||数据集id|
|fileVos|[[FileVo](#schemafilevo)]|false|none||文件|

<h2 id="tocS_FileVo">FileVo</h2>

<a id="schemafilevo"></a>
<a id="schema_FileVo"></a>
<a id="tocSfilevo"></a>
<a id="tocsfilevo"></a>

```json
{
  "fileName": "9672",
  "fileType": 0,
  "fileId": 123423423423445
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|fileName|string|false|none||文件名称|
|fileType|integer|false|none||文件类型|
|fileId|integer(int64)|false|none||文件ID|

<h2 id="tocS_AlarmDetailsSaveReqVO">AlarmDetailsSaveReqVO</h2>

<a id="schemaalarmdetailssavereqvo"></a>
<a id="schema_AlarmDetailsSaveReqVO"></a>
<a id="tocSalarmdetailssavereqvo"></a>
<a id="tocsalarmdetailssavereqvo"></a>

```json
{
  "id": 17200,
  "taskId": 22076,
  "taskFileId": 27440,
  "alarmType": "2",
  "alarmTypeName": "张三",
  "confidence": "string",
  "alarmFileUrl": "https://www.iocoder.cn",
  "alarmFileId": "15350",
  "alarmTime": "string",
  "createTime": "string",
  "creator": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "tenantId": 16799
}

```

管理后台 - 告警信息-明细新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|taskId|integer(int64)|false|none||识别任务id|
|taskFileId|integer(int64)|false|none||关联的任务文件id|
|alarmType|string|false|none||隐患类型|
|alarmTypeName|string|false|none||隐患类型名称|
|confidence|string|false|none||置信度|
|alarmFileUrl|string|false|none||结果文件url，视频隐患截图|
|alarmFileId|string|false|none||结果文件id，视频隐患截图|
|alarmTime|string|false|none||发生隐患的视频第几秒|
|createTime|string|false|none||创建时间|
|creator|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updater|string|false|none||更新人|
|deleted|boolean|false|none||0代表存在 2代表删除|
|tenantId|integer(int64)|false|none||租户编号|

<h2 id="tocS_AiModelSaveReqVO">AiModelSaveReqVO</h2>

<a id="schemaaimodelsavereqvo"></a>
<a id="schema_AiModelSaveReqVO"></a>
<a id="tocSaimodelsavereqvo"></a>
<a id="tocsaimodelsavereqvo"></a>

```json
{
  "id": 24150,
  "modelName": "张三",
  "modelFileId": "14429",
  "modelVideoFileId": "19647",
  "coverImageFileId": 14429,
  "status": 0,
  "createTime": "string",
  "creator": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "tenantId": 17682
}

```

管理后台 - 后识别AI模型管理新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|modelName|string|true|none||模型名称|
|modelFileId|string|true|none||关联的模型文件ID|
|modelVideoFileId|string|false|none||模型宣传视频文件id|
|coverImageFileId|integer(int64)|false|none||模型封面文件ID|
|status|integer|false|none||启用状态：0-停用，1-启用|
|createTime|string|false|none||创建时间|
|creator|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updater|string|false|none||更新人|
|deleted|boolean|false|none||0代表存在 2代表删除|
|tenantId|integer(int64)|false|none||租户编号|

<h2 id="tocS_CommonResultString">CommonResultString</h2>

<a id="schemacommonresultstring"></a>
<a id="schema_CommonResultString"></a>
<a id="tocScommonresultstring"></a>
<a id="tocscommonresultstring"></a>

```json
{
  "code": 0,
  "data": "string",
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|string|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_FileCreateReqVO">FileCreateReqVO</h2>

<a id="schemafilecreatereqvo"></a>
<a id="schema_FileCreateReqVO"></a>
<a id="tocSfilecreatereqvo"></a>
<a id="tocsfilecreatereqvo"></a>

```json
{
  "configId": 11,
  "path": "yudao.jpg",
  "name": "yudao.jpg",
  "url": "https://www.iocoder.cn/yudao.jpg",
  "type": "application/octet-stream",
  "size": 2048
}

```

管理后台 - 文件创建 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|configId|integer(int64)|true|none||文件配置编号|
|path|string|true|none||文件路径|
|name|string|true|none||原文件名|
|url|string|true|none||文件 URL|
|type|string|false|none||文件 MIME 类型|
|size|integer|false|none||文件大小|

<h2 id="tocS_CommonResultLong">CommonResultLong</h2>

<a id="schemacommonresultlong"></a>
<a id="schema_CommonResultLong"></a>
<a id="tocScommonresultlong"></a>
<a id="tocscommonresultlong"></a>

```json
{
  "code": 0,
  "data": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|integer(int64)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultFilePresignedUrlRespVO">CommonResultFilePresignedUrlRespVO</h2>

<a id="schemacommonresultfilepresignedurlrespvo"></a>
<a id="schema_CommonResultFilePresignedUrlRespVO"></a>
<a id="tocScommonresultfilepresignedurlrespvo"></a>
<a id="tocscommonresultfilepresignedurlrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "configId": 11,
    "uploadUrl": "https://s3.cn-south-1.qiniucs.com/ruoyi-vue-pro/758d3a5387507358c7236de4c8f96de1c7f5097ff6a7722b34772fb7b76b140f.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=3TvrJ70gl2Gt6IBe7_IZT1F6i_k0iMuRtyEv4EyS%2F20240217%2Fcn-south-1%2Fs3%2Faws4_request&X-Amz-Date=20240217T123222Z&X-Amz-Expires=600&X-Amz-SignedHeaders=host&X-Amz-Signature=a29f33770ab79bf523ccd4034d0752ac545f3c2a3b17baa1eb4e280cfdccfda5",
    "url": "https://test.yudao.iocoder.cn/758d3a5387507358c7236de4c8f96de1c7f5097ff6a7722b34772fb7b76b140f.png",
    "path": "xxx.png"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[FilePresignedUrlRespVO](#schemafilepresignedurlrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_FilePresignedUrlRespVO">FilePresignedUrlRespVO</h2>

<a id="schemafilepresignedurlrespvo"></a>
<a id="schema_FilePresignedUrlRespVO"></a>
<a id="tocSfilepresignedurlrespvo"></a>
<a id="tocsfilepresignedurlrespvo"></a>

```json
{
  "configId": 11,
  "uploadUrl": "https://s3.cn-south-1.qiniucs.com/ruoyi-vue-pro/758d3a5387507358c7236de4c8f96de1c7f5097ff6a7722b34772fb7b76b140f.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=3TvrJ70gl2Gt6IBe7_IZT1F6i_k0iMuRtyEv4EyS%2F20240217%2Fcn-south-1%2Fs3%2Faws4_request&X-Amz-Date=20240217T123222Z&X-Amz-Expires=600&X-Amz-SignedHeaders=host&X-Amz-Signature=a29f33770ab79bf523ccd4034d0752ac545f3c2a3b17baa1eb4e280cfdccfda5",
  "url": "https://test.yudao.iocoder.cn/758d3a5387507358c7236de4c8f96de1c7f5097ff6a7722b34772fb7b76b140f.png",
  "path": "xxx.png"
}

```

管理后台 - 文件预签名地址 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|configId|integer(int64)|false|none||配置编号|
|uploadUrl|string|false|none||文件上传 URL|
|url|string|false|none||文件访问 URL|
|path|string|false|none||文件路径|

<h2 id="tocS_CompletedPart">CompletedPart</h2>

<a id="schemacompletedpart"></a>
<a id="schema_CompletedPart"></a>
<a id="tocScompletedpart"></a>
<a id="tocscompletedpart"></a>

```json
{
  "eTag": "string",
  "checksumCRC32": "string",
  "checksumCRC32C": "string",
  "checksumCRC64NVME": "string",
  "checksumSHA1": "string",
  "checksumSHA256": "string",
  "partNumber": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|eTag|string|false|none||none|
|checksumCRC32|string|false|none||none|
|checksumCRC32C|string|false|none||none|
|checksumCRC64NVME|string|false|none||none|
|checksumSHA1|string|false|none||none|
|checksumSHA256|string|false|none||none|
|partNumber|integer|false|none||none|

<h2 id="tocS_CommonResultPageResultFileRespVO">CommonResultPageResultFileRespVO</h2>

<a id="schemacommonresultpageresultfilerespvo"></a>
<a id="schema_CommonResultPageResultFileRespVO"></a>
<a id="tocScommonresultpageresultfilerespvo"></a>
<a id="tocscommonresultpageresultfilerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1024,
        "configId": 11,
        "path": "yudao.jpg",
        "name": "yudao.jpg",
        "url": "https://www.iocoder.cn/yudao.jpg",
        "type": "application/octet-stream",
        "size": 2048,
        "createTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultFileRespVO](#schemapageresultfilerespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_FileRespVO">FileRespVO</h2>

<a id="schemafilerespvo"></a>
<a id="schema_FileRespVO"></a>
<a id="tocSfilerespvo"></a>
<a id="tocsfilerespvo"></a>

```json
{
  "id": 1024,
  "configId": 11,
  "path": "yudao.jpg",
  "name": "yudao.jpg",
  "url": "https://www.iocoder.cn/yudao.jpg",
  "type": "application/octet-stream",
  "size": 2048,
  "createTime": "string"
}

```

管理后台 - 文件 Response VO,不返回 content 字段，太大

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||文件编号|
|configId|integer(int64)|false|none||配置编号|
|path|string|false|none||文件路径|
|name|string|false|none||原文件名|
|url|string|false|none||文件 URL|
|type|string|false|none||文件MIME类型|
|size|integer|false|none||文件大小|
|createTime|string|false|none||创建时间|

<h2 id="tocS_PageResultFileRespVO">PageResultFileRespVO</h2>

<a id="schemapageresultfilerespvo"></a>
<a id="schema_PageResultFileRespVO"></a>
<a id="tocSpageresultfilerespvo"></a>
<a id="tocspageresultfilerespvo"></a>

```json
{
  "list": [
    {
      "id": 1024,
      "configId": 11,
      "path": "yudao.jpg",
      "name": "yudao.jpg",
      "url": "https://www.iocoder.cn/yudao.jpg",
      "type": "application/octet-stream",
      "size": 2048,
      "createTime": "string"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[FileRespVO](#schemafilerespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_CommonResultPageResultFileConfigRespVO">CommonResultPageResultFileConfigRespVO</h2>

<a id="schemacommonresultpageresultfileconfigrespvo"></a>
<a id="schema_CommonResultPageResultFileConfigRespVO"></a>
<a id="tocScommonresultpageresultfileconfigrespvo"></a>
<a id="tocscommonresultpageresultfileconfigrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "S3 - 阿里云",
        "storage": 1,
        "master": true,
        "config": {},
        "remark": "我是备注",
        "createTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultFileConfigRespVO](#schemapageresultfileconfigrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_FileClientConfig">FileClientConfig</h2>

<a id="schemafileclientconfig"></a>
<a id="schema_FileClientConfig"></a>
<a id="tocSfileclientconfig"></a>
<a id="tocsfileclientconfig"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_FileConfigRespVO">FileConfigRespVO</h2>

<a id="schemafileconfigrespvo"></a>
<a id="schema_FileConfigRespVO"></a>
<a id="tocSfileconfigrespvo"></a>
<a id="tocsfileconfigrespvo"></a>

```json
{
  "id": 1,
  "name": "S3 - 阿里云",
  "storage": 1,
  "master": true,
  "config": {},
  "remark": "我是备注",
  "createTime": "string"
}

```

管理后台 - 文件配置 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||编号|
|name|string|false|none||配置名|
|storage|integer|false|none||存储器，参见 FileStorageEnum 枚举类|
|master|boolean|false|none||是否为主配置|
|config|[FileClientConfig](#schemafileclientconfig)|false|none||存储配置|
|remark|string|false|none||备注|
|createTime|string|false|none||创建时间|

<h2 id="tocS_PageResultFileConfigRespVO">PageResultFileConfigRespVO</h2>

<a id="schemapageresultfileconfigrespvo"></a>
<a id="schema_PageResultFileConfigRespVO"></a>
<a id="tocSpageresultfileconfigrespvo"></a>
<a id="tocspageresultfileconfigrespvo"></a>

```json
{
  "list": [
    {
      "id": 1,
      "name": "S3 - 阿里云",
      "storage": 1,
      "master": true,
      "config": {},
      "remark": "我是备注",
      "createTime": "string"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[FileConfigRespVO](#schemafileconfigrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_CommonResultFileConfigRespVO">CommonResultFileConfigRespVO</h2>

<a id="schemacommonresultfileconfigrespvo"></a>
<a id="schema_CommonResultFileConfigRespVO"></a>
<a id="tocScommonresultfileconfigrespvo"></a>
<a id="tocscommonresultfileconfigrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "name": "S3 - 阿里云",
    "storage": 1,
    "master": true,
    "config": {},
    "remark": "我是备注",
    "createTime": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[FileConfigRespVO](#schemafileconfigrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultTaskRespVO">CommonResultPageResultTaskRespVO</h2>

<a id="schemacommonresultpageresulttaskrespvo"></a>
<a id="schema_CommonResultPageResultTaskRespVO"></a>
<a id="tocScommonresultpageresulttaskrespvo"></a>
<a id="tocscommonresultpageresulttaskrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 27454,
        "name": "赵六",
        "taskType": 2,
        "taskDataId": 29021,
        "status": 1,
        "aiModelId": "5308",
        "aiModelName": "芋艿",
        "alarmNoticeUserId": "2717",
        "reason": "不好",
        "creator": "string",
        "createTime": "string",
        "updater": "string",
        "deleted": true,
        "updateTime": "string",
        "tenantId": 9293
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultTaskRespVO](#schemapageresulttaskrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_PageResultTaskRespVO">PageResultTaskRespVO</h2>

<a id="schemapageresulttaskrespvo"></a>
<a id="schema_PageResultTaskRespVO"></a>
<a id="tocSpageresulttaskrespvo"></a>
<a id="tocspageresulttaskrespvo"></a>

```json
{
  "list": [
    {
      "id": 27454,
      "name": "赵六",
      "taskType": 2,
      "taskDataId": 29021,
      "status": 1,
      "aiModelId": "5308",
      "aiModelName": "芋艿",
      "alarmNoticeUserId": "2717",
      "reason": "不好",
      "creator": "string",
      "createTime": "string",
      "updater": "string",
      "deleted": true,
      "updateTime": "string",
      "tenantId": 9293
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[TaskRespVO](#schemataskrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_TaskRespVO">TaskRespVO</h2>

<a id="schemataskrespvo"></a>
<a id="schema_TaskRespVO"></a>
<a id="tocStaskrespvo"></a>
<a id="tocstaskrespvo"></a>

```json
{
  "id": 27454,
  "name": "赵六",
  "taskType": 2,
  "taskDataId": 29021,
  "status": 1,
  "aiModelId": "5308",
  "aiModelName": "芋艿",
  "alarmNoticeUserId": "2717",
  "reason": "不好",
  "creator": "string",
  "createTime": "string",
  "updater": "string",
  "deleted": true,
  "updateTime": "string",
  "tenantId": 9293
}

```

管理后台 - ai识别任务 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|name|string|false|none||识别报告名称|
|taskType|integer|false|none||任务类型 0数据集；1视频流|
|taskDataId|integer(int64)|false|none||数据集或者视频流的id，类型根据task_type判断|
|status|integer|false|none||识别状态|
|aiModelId|string|false|none||识别模型id|
|aiModelName|string|false|none||识别模型名称|
|alarmNoticeUserId|string|false|none||告警通知人员id，多个用逗号隔开|
|reason|string|false|none||识别失败原因|
|creator|string|false|none||创建人|
|createTime|string|false|none||创建时间|
|updater|string|false|none||更新人|
|deleted|boolean|false|none||0代表存在 2代表删除|
|updateTime|string|false|none||更新时间|
|tenantId|integer(int64)|false|none||租户编号|

<h2 id="tocS_CommonResultTaskRespVO">CommonResultTaskRespVO</h2>

<a id="schemacommonresulttaskrespvo"></a>
<a id="schema_CommonResultTaskRespVO"></a>
<a id="tocScommonresulttaskrespvo"></a>
<a id="tocscommonresulttaskrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 27454,
    "name": "赵六",
    "taskType": 2,
    "taskDataId": 29021,
    "status": 1,
    "aiModelId": "5308",
    "aiModelName": "芋艿",
    "alarmNoticeUserId": "2717",
    "reason": "不好",
    "creator": "string",
    "createTime": "string",
    "updater": "string",
    "deleted": true,
    "updateTime": "string",
    "tenantId": 9293
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[TaskRespVO](#schemataskrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultTaskFilesRespVO">CommonResultPageResultTaskFilesRespVO</h2>

<a id="schemacommonresultpageresulttaskfilesrespvo"></a>
<a id="schema_CommonResultPageResultTaskFilesRespVO"></a>
<a id="tocScommonresultpageresulttaskfilesrespvo"></a>
<a id="tocscommonresultpageresulttaskfilesrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 32320,
        "taskId": 2269,
        "taskFileId": 9678,
        "type": 2,
        "originalFileUrl": "https://www.iocoder.cn",
        "processedFileUrl": "https://www.iocoder.cn",
        "processedFileId": "24399",
        "hasAlarm": 0,
        "status": 1,
        "creator": "string",
        "resultJsonFileId": "12959",
        "updater": "string",
        "resultJsonFileUrl": "https://www.iocoder.cn",
        "deleted": true,
        "createTime": "string",
        "tenantId": 26007,
        "updateTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultTaskFilesRespVO](#schemapageresulttaskfilesrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_PageResultTaskFilesRespVO">PageResultTaskFilesRespVO</h2>

<a id="schemapageresulttaskfilesrespvo"></a>
<a id="schema_PageResultTaskFilesRespVO"></a>
<a id="tocSpageresulttaskfilesrespvo"></a>
<a id="tocspageresulttaskfilesrespvo"></a>

```json
{
  "list": [
    {
      "id": 32320,
      "taskId": 2269,
      "taskFileId": 9678,
      "type": 2,
      "originalFileUrl": "https://www.iocoder.cn",
      "processedFileUrl": "https://www.iocoder.cn",
      "processedFileId": "24399",
      "hasAlarm": 0,
      "status": 1,
      "creator": "string",
      "resultJsonFileId": "12959",
      "updater": "string",
      "resultJsonFileUrl": "https://www.iocoder.cn",
      "deleted": true,
      "createTime": "string",
      "tenantId": 26007,
      "updateTime": "string"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[TaskFilesRespVO](#schemataskfilesrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_TaskFilesRespVO">TaskFilesRespVO</h2>

<a id="schemataskfilesrespvo"></a>
<a id="schema_TaskFilesRespVO"></a>
<a id="tocStaskfilesrespvo"></a>
<a id="tocstaskfilesrespvo"></a>

```json
{
  "id": 32320,
  "taskId": 2269,
  "taskFileId": 9678,
  "type": 2,
  "originalFileUrl": "https://www.iocoder.cn",
  "processedFileUrl": "https://www.iocoder.cn",
  "processedFileId": "24399",
  "hasAlarm": 0,
  "status": 1,
  "creator": "string",
  "resultJsonFileId": "12959",
  "updater": "string",
  "resultJsonFileUrl": "https://www.iocoder.cn",
  "deleted": true,
  "createTime": "string",
  "tenantId": 26007,
  "updateTime": "string"
}

```

管理后台 - 任务文件：视频、图片 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|taskId|integer(int64)|false|none||识别任务id|
|taskFileId|integer(int64)|false|none||关联的识别文件id|
|type|integer|false|none||文件类型：0-图片，1-视频|
|originalFileUrl|string|false|none||被识别的文件url|
|processedFileUrl|string|false|none||识别后的文件url|
|processedFileId|string|false|none||识别后的文件id|
|hasAlarm|integer|false|none||是否有隐患：0-否，1-是|
|status|integer|false|none||识别状态：0-识别中，10-识别完成，20-识别失败|
|creator|string|false|none||创建人|
|resultJsonFileId|string|false|none||识别结果json文件id|
|updater|string|false|none||更新人|
|resultJsonFileUrl|string|false|none||识别结果json文件url|
|deleted|boolean|false|none||0代表存在 2代表删除|
|createTime|string|false|none||创建时间|
|tenantId|integer(int64)|false|none||租户编号|
|updateTime|string|false|none||更新时间|

<h2 id="tocS_CommonResultTaskFilesRespVO">CommonResultTaskFilesRespVO</h2>

<a id="schemacommonresulttaskfilesrespvo"></a>
<a id="schema_CommonResultTaskFilesRespVO"></a>
<a id="tocScommonresulttaskfilesrespvo"></a>
<a id="tocscommonresulttaskfilesrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 32320,
    "taskId": 2269,
    "taskFileId": 9678,
    "type": 2,
    "originalFileUrl": "https://www.iocoder.cn",
    "processedFileUrl": "https://www.iocoder.cn",
    "processedFileId": "24399",
    "hasAlarm": 0,
    "status": 1,
    "creator": "string",
    "resultJsonFileId": "12959",
    "updater": "string",
    "resultJsonFileUrl": "https://www.iocoder.cn",
    "deleted": true,
    "createTime": "string",
    "tenantId": 26007,
    "updateTime": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[TaskFilesRespVO](#schemataskfilesrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultResourceDeviceRespVO">CommonResultPageResultResourceDeviceRespVO</h2>

<a id="schemacommonresultpageresultresourcedevicerespvo"></a>
<a id="schema_CommonResultPageResultResourceDeviceRespVO"></a>
<a id="tocScommonresultpageresultresourcedevicerespvo"></a>
<a id="tocscommonresultpageresultresourcedevicerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 28557,
        "deviceCode": "string",
        "deviceName": "张三",
        "ip": "string",
        "port": 0,
        "description": "你猜",
        "username": "李四",
        "password": "string",
        "channel": "string",
        "liveUrl": "https://www.iocoder.cn",
        "status": 1,
        "createTime": "string",
        "creator": 0,
        "updateTime": "string",
        "updater": "string",
        "deleted": true,
        "deptId": 31191,
        "tenantId": 30909
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultResourceDeviceRespVO](#schemapageresultresourcedevicerespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_PageResultResourceDeviceRespVO">PageResultResourceDeviceRespVO</h2>

<a id="schemapageresultresourcedevicerespvo"></a>
<a id="schema_PageResultResourceDeviceRespVO"></a>
<a id="tocSpageresultresourcedevicerespvo"></a>
<a id="tocspageresultresourcedevicerespvo"></a>

```json
{
  "list": [
    {
      "id": 28557,
      "deviceCode": "string",
      "deviceName": "张三",
      "ip": "string",
      "port": 0,
      "description": "你猜",
      "username": "李四",
      "password": "string",
      "channel": "string",
      "liveUrl": "https://www.iocoder.cn",
      "status": 1,
      "createTime": "string",
      "creator": 0,
      "updateTime": "string",
      "updater": "string",
      "deleted": true,
      "deptId": 31191,
      "tenantId": 30909
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[ResourceDeviceRespVO](#schemaresourcedevicerespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_ResourceDeviceRespVO">ResourceDeviceRespVO</h2>

<a id="schemaresourcedevicerespvo"></a>
<a id="schema_ResourceDeviceRespVO"></a>
<a id="tocSresourcedevicerespvo"></a>
<a id="tocsresourcedevicerespvo"></a>

```json
{
  "id": 28557,
  "deviceCode": "string",
  "deviceName": "张三",
  "ip": "string",
  "port": 0,
  "description": "你猜",
  "username": "李四",
  "password": "string",
  "channel": "string",
  "liveUrl": "https://www.iocoder.cn",
  "status": 1,
  "createTime": "string",
  "creator": 0,
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "deptId": 31191,
  "tenantId": 30909
}

```

管理后台 - 资源设备 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键ID|
|deviceCode|string|false|none||设备编号|
|deviceName|string|false|none||设备名称|
|ip|string|false|none||ip地址|
|port|integer|false|none||端口|
|description|string|false|none||描述|
|username|string|false|none||设备账号|
|password|string|false|none||密码|
|channel|string|false|none||视频流通道|
|liveUrl|string|false|none||视频链接|
|status|integer|false|none||0-离线，10-在线|
|createTime|string|false|none||创建时间|
|creator|integer(int64)|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updater|string|false|none||更新人|
|deleted|boolean|false|none||删除标志（0代表存在 2代表删除）|
|deptId|integer(int64)|false|none||所属部门|
|tenantId|integer(int64)|false|none||租户编号|

<h2 id="tocS_CommonResultResourceDeviceRespVO">CommonResultResourceDeviceRespVO</h2>

<a id="schemacommonresultresourcedevicerespvo"></a>
<a id="schema_CommonResultResourceDeviceRespVO"></a>
<a id="tocScommonresultresourcedevicerespvo"></a>
<a id="tocscommonresultresourcedevicerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 28557,
    "deviceCode": "string",
    "deviceName": "张三",
    "ip": "string",
    "port": 0,
    "description": "你猜",
    "username": "李四",
    "password": "string",
    "channel": "string",
    "liveUrl": "https://www.iocoder.cn",
    "status": 1,
    "createTime": "string",
    "creator": 0,
    "updateTime": "string",
    "updater": "string",
    "deleted": true,
    "deptId": 31191,
    "tenantId": 30909
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[ResourceDeviceRespVO](#schemaresourcedevicerespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultDatasetRespVO">CommonResultPageResultDatasetRespVO</h2>

<a id="schemacommonresultpageresultdatasetrespvo"></a>
<a id="schema_CommonResultPageResultDatasetRespVO"></a>
<a id="tocScommonresultpageresultdatasetrespvo"></a>
<a id="tocscommonresultpageresultdatasetrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 11095,
        "name": "李四",
        "status": 1,
        "markFlag": 0,
        "createTime": "string",
        "creator": "string",
        "creatorName": "string",
        "updater": "string",
        "deleted": "string",
        "dataSourceSize": 0,
        "dataSourceNum": 0,
        "datasetFiles": [
          {
            "id": null,
            "datasetId": null,
            "datasetIds": null,
            "fileName": null,
            "type": null,
            "fileId": null,
            "aiProcessed": null,
            "createTime": null,
            "updateTime": null,
            "updater": null,
            "deleted": null,
            "createDept": null,
            "creator": null,
            "fileUrl": null,
            "fileSize": null
          }
        ],
        "coverImageFileId": 14429,
        "coverImageFileUrl": null
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultDatasetRespVO](#schemapageresultdatasetrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_DatasetRespVO">DatasetRespVO</h2>

<a id="schemadatasetrespvo"></a>
<a id="schema_DatasetRespVO"></a>
<a id="tocSdatasetrespvo"></a>
<a id="tocsdatasetrespvo"></a>

```json
{
  "id": 11095,
  "name": "李四",
  "status": 1,
  "markFlag": 0,
  "createTime": "string",
  "creator": "string",
  "creatorName": "string",
  "updater": "string",
  "deleted": "string",
  "dataSourceSize": 0,
  "dataSourceNum": 0,
  "datasetFiles": [
    {
      "id": 1750,
      "datasetId": 21267,
      "datasetIds": "21267",
      "fileName": "王五",
      "type": 1,
      "fileId": 9672,
      "aiProcessed": 0,
      "createTime": "string",
      "updateTime": "string",
      "updater": "string",
      "deleted": "string",
      "createDept": 0,
      "creator": "string",
      "fileUrl": "string",
      "fileSize": 0
    }
  ],
  "coverImageFileId": 14429,
  "coverImageFileUrl": null
}

```

管理后台 - 数据集 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|name|string|false|none||数据集名称|
|status|integer|false|none||上传状态|
|markFlag|integer|false|none||是否标注 0 否；1是|
|createTime|string|false|none||创建时间|
|creator|string|false|none||创建人|
|creatorName|string|false|none||创建人姓名|
|updater|string|false|none||更新人|
|deleted|string|false|none||0代表存在 2代表删除|
|dataSourceSize|number|false|none||数据集文件大小 kb|
|dataSourceNum|integer|false|none||数据集文件数量|
|datasetFiles|[[DatasetFilesRespVO](#schemadatasetfilesrespvo)]|false|none||数据集文件列表|
|coverImageFileId|integer(int64)|false|none||数据集封面文件ID|
|coverImageFileUrl|integer(int64)|false|none||数据集封面文件Url|

<h2 id="tocS_PageResultDatasetRespVO">PageResultDatasetRespVO</h2>

<a id="schemapageresultdatasetrespvo"></a>
<a id="schema_PageResultDatasetRespVO"></a>
<a id="tocSpageresultdatasetrespvo"></a>
<a id="tocspageresultdatasetrespvo"></a>

```json
{
  "list": [
    {
      "id": 11095,
      "name": "李四",
      "status": 1,
      "markFlag": 0,
      "createTime": "string",
      "creator": "string",
      "creatorName": "string",
      "updater": "string",
      "deleted": "string",
      "dataSourceSize": 0,
      "dataSourceNum": 0,
      "datasetFiles": [
        {
          "id": 1750,
          "datasetId": 21267,
          "datasetIds": "21267",
          "fileName": "王五",
          "type": 1,
          "fileId": 9672,
          "aiProcessed": 0,
          "createTime": "string",
          "updateTime": "string",
          "updater": "string",
          "deleted": "string",
          "createDept": 0,
          "creator": "string",
          "fileUrl": "string",
          "fileSize": 0
        }
      ],
      "coverImageFileId": 14429,
      "coverImageFileUrl": null
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[DatasetRespVO](#schemadatasetrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_CommonResultDatasetRespVO">CommonResultDatasetRespVO</h2>

<a id="schemacommonresultdatasetrespvo"></a>
<a id="schema_CommonResultDatasetRespVO"></a>
<a id="tocScommonresultdatasetrespvo"></a>
<a id="tocscommonresultdatasetrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 11095,
    "name": "李四",
    "status": 1,
    "markFlag": 0,
    "createTime": "string",
    "creator": "string",
    "creatorName": "string",
    "updater": "string",
    "deleted": "string",
    "dataSourceSize": 0,
    "dataSourceNum": 0,
    "datasetFiles": [
      {
        "id": 1750,
        "datasetId": 21267,
        "datasetIds": "21267",
        "fileName": "王五",
        "type": 1,
        "fileId": 9672,
        "aiProcessed": 0,
        "createTime": "string",
        "updateTime": "string",
        "updater": "string",
        "deleted": "string",
        "createDept": 0,
        "creator": "string",
        "fileUrl": "string",
        "fileSize": 0
      }
    ],
    "coverImageFileId": 14429,
    "coverImageFileUrl": null
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[DatasetRespVO](#schemadatasetrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultDatasetFilesRespVO">CommonResultPageResultDatasetFilesRespVO</h2>

<a id="schemacommonresultpageresultdatasetfilesrespvo"></a>
<a id="schema_CommonResultPageResultDatasetFilesRespVO"></a>
<a id="tocScommonresultpageresultdatasetfilesrespvo"></a>
<a id="tocscommonresultpageresultdatasetfilesrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1750,
        "datasetId": 21267,
        "datasetIds": "21267",
        "fileName": "王五",
        "type": 1,
        "fileId": 9672,
        "aiProcessed": 0,
        "createTime": "string",
        "updateTime": "string",
        "updater": "string",
        "deleted": "string",
        "createDept": 0,
        "creator": "string",
        "fileUrl": "string",
        "fileSize": 0
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultDatasetFilesRespVO](#schemapageresultdatasetfilesrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_DatasetFilesRespVO">DatasetFilesRespVO</h2>

<a id="schemadatasetfilesrespvo"></a>
<a id="schema_DatasetFilesRespVO"></a>
<a id="tocSdatasetfilesrespvo"></a>
<a id="tocsdatasetfilesrespvo"></a>

```json
{
  "id": 1750,
  "datasetId": 21267,
  "datasetIds": "21267",
  "fileName": "王五",
  "type": 1,
  "fileId": 9672,
  "aiProcessed": 0,
  "createTime": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": "string",
  "createDept": 0,
  "creator": "string",
  "fileUrl": "string",
  "fileSize": 0
}

```

管理后台 - 数据集文件信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|datasetId|integer(int64)|false|none||数据集id|
|datasetIds|[integer]|false|none||数据集ids|
|fileName|string|false|none||文件名称|
|type|integer|false|none||文件类型：0-图片，1-视频|
|fileId|integer(int64)|false|none||文件ID|
|aiProcessed|integer|false|none||ai识别标识：0-未识别过，1-已识别过|
|createTime|string|false|none||创建时间|
|updateTime|string|false|none||更新时间|
|updater|string|false|none||更新人|
|deleted|string|false|none||0代表存在 2代表删除|
|createDept|integer(int64)|false|none||创建部门|
|creator|string|false|none||创建人|
|fileUrl|string|false|none||文件url|
|fileSize|integer|false|none||文件大小|

<h2 id="tocS_PageResultDatasetFilesRespVO">PageResultDatasetFilesRespVO</h2>

<a id="schemapageresultdatasetfilesrespvo"></a>
<a id="schema_PageResultDatasetFilesRespVO"></a>
<a id="tocSpageresultdatasetfilesrespvo"></a>
<a id="tocspageresultdatasetfilesrespvo"></a>

```json
{
  "list": [
    {
      "id": 1750,
      "datasetId": 21267,
      "datasetIds": "21267",
      "fileName": "王五",
      "type": 1,
      "fileId": 9672,
      "aiProcessed": 0,
      "createTime": "string",
      "updateTime": "string",
      "updater": "string",
      "deleted": "string",
      "createDept": 0,
      "creator": "string",
      "fileUrl": "string",
      "fileSize": 0
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[DatasetFilesRespVO](#schemadatasetfilesrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_CommonResultDatasetFilesRespVO">CommonResultDatasetFilesRespVO</h2>

<a id="schemacommonresultdatasetfilesrespvo"></a>
<a id="schema_CommonResultDatasetFilesRespVO"></a>
<a id="tocScommonresultdatasetfilesrespvo"></a>
<a id="tocscommonresultdatasetfilesrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1750,
    "datasetId": 21267,
    "datasetIds": "21267",
    "fileName": "王五",
    "type": 1,
    "fileId": 9672,
    "aiProcessed": 0,
    "createTime": "string",
    "updateTime": "string",
    "updater": "string",
    "deleted": "string",
    "createDept": 0,
    "creator": "string",
    "fileUrl": "string",
    "fileSize": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[DatasetFilesRespVO](#schemadatasetfilesrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_AlarmDetailsRespVO">AlarmDetailsRespVO</h2>

<a id="schemaalarmdetailsrespvo"></a>
<a id="schema_AlarmDetailsRespVO"></a>
<a id="tocSalarmdetailsrespvo"></a>
<a id="tocsalarmdetailsrespvo"></a>

```json
{
  "id": 17200,
  "taskId": 22076,
  "taskFileId": 27440,
  "alarmType": "2",
  "alarmTypeName": "张三",
  "confidence": "string",
  "alarmFileUrl": "https://www.iocoder.cn",
  "alarmFileId": "15350",
  "alarmTime": "string",
  "createTime": "string",
  "creator": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "tenantId": 16799
}

```

管理后台 - 告警信息-明细 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|taskId|integer(int64)|false|none||识别任务id|
|taskFileId|integer(int64)|false|none||关联的任务文件id|
|alarmType|string|false|none||隐患类型|
|alarmTypeName|string|false|none||隐患类型名称|
|confidence|string|false|none||置信度|
|alarmFileUrl|string|false|none||结果文件url，视频隐患截图|
|alarmFileId|string|false|none||结果文件id，视频隐患截图|
|alarmTime|string|false|none||发生隐患的视频第几秒|
|createTime|string|false|none||创建时间|
|creator|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updater|string|false|none||更新人|
|deleted|boolean|false|none||0代表存在 2代表删除|
|tenantId|integer(int64)|false|none||租户编号|

<h2 id="tocS_CommonResultPageResultAlarmDetailsRespVO">CommonResultPageResultAlarmDetailsRespVO</h2>

<a id="schemacommonresultpageresultalarmdetailsrespvo"></a>
<a id="schema_CommonResultPageResultAlarmDetailsRespVO"></a>
<a id="tocScommonresultpageresultalarmdetailsrespvo"></a>
<a id="tocscommonresultpageresultalarmdetailsrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 17200,
        "taskId": 22076,
        "taskFileId": 27440,
        "alarmType": "2",
        "alarmTypeName": "张三",
        "confidence": "string",
        "alarmFileUrl": "https://www.iocoder.cn",
        "alarmFileId": "15350",
        "alarmTime": "string",
        "createTime": "string",
        "creator": "string",
        "updateTime": "string",
        "updater": "string",
        "deleted": true,
        "tenantId": 16799
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAlarmDetailsRespVO](#schemapageresultalarmdetailsrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_PageResultAlarmDetailsRespVO">PageResultAlarmDetailsRespVO</h2>

<a id="schemapageresultalarmdetailsrespvo"></a>
<a id="schema_PageResultAlarmDetailsRespVO"></a>
<a id="tocSpageresultalarmdetailsrespvo"></a>
<a id="tocspageresultalarmdetailsrespvo"></a>

```json
{
  "list": [
    {
      "id": 17200,
      "taskId": 22076,
      "taskFileId": 27440,
      "alarmType": "2",
      "alarmTypeName": "张三",
      "confidence": "string",
      "alarmFileUrl": "https://www.iocoder.cn",
      "alarmFileId": "15350",
      "alarmTime": "string",
      "createTime": "string",
      "creator": "string",
      "updateTime": "string",
      "updater": "string",
      "deleted": true,
      "tenantId": 16799
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AlarmDetailsRespVO](#schemaalarmdetailsrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_CommonResultAlarmDetailsRespVO">CommonResultAlarmDetailsRespVO</h2>

<a id="schemacommonresultalarmdetailsrespvo"></a>
<a id="schema_CommonResultAlarmDetailsRespVO"></a>
<a id="tocScommonresultalarmdetailsrespvo"></a>
<a id="tocscommonresultalarmdetailsrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 17200,
    "taskId": 22076,
    "taskFileId": 27440,
    "alarmType": "2",
    "alarmTypeName": "张三",
    "confidence": "string",
    "alarmFileUrl": "https://www.iocoder.cn",
    "alarmFileId": "15350",
    "alarmTime": "string",
    "createTime": "string",
    "creator": "string",
    "updateTime": "string",
    "updater": "string",
    "deleted": true,
    "tenantId": 16799
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AlarmDetailsRespVO](#schemaalarmdetailsrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_AiModelRespVO">AiModelRespVO</h2>

<a id="schemaaimodelrespvo"></a>
<a id="schema_AiModelRespVO"></a>
<a id="tocSaimodelrespvo"></a>
<a id="tocsaimodelrespvo"></a>

```json
{
  "id": 24150,
  "modelName": "张三",
  "modelFileId": "14429",
  "modelFileUrl": "http://baidu.com",
  "coverImageFileId": 14429,
  "coverImageFileUrl": 14429,
  "modelVideoFileId": "19647",
  "modelVideoFileUrl": "http://baidu.cc",
  "status": 0,
  "createTime": "string",
  "creator": "string",
  "updateTime": "string",
  "updater": "string",
  "deleted": true,
  "tenantId": 17682
}

```

管理后台 - 后识别AI模型管理 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||ID|
|modelName|string|false|none||模型名称|
|modelFileId|string|false|none||关联的模型文件ID|
|modelFileUrl|string|false|none||关联的模型文件Url|
|coverImageFileId|integer(int64)|false|none||模型封面文件ID|
|coverImageFileUrl|integer(int64)|false|none||模型封面文件Url|
|modelVideoFileId|string|false|none||模型宣传视频文件id|
|modelVideoFileUrl|string|false|none||模型宣传视频文件Url|
|status|integer|false|none||启用状态：0-停用，1-启用|
|createTime|string|false|none||创建时间|
|creator|string|false|none||创建人|
|updateTime|string|false|none||更新时间|
|updater|string|false|none||更新人|
|deleted|boolean|false|none||0代表存在 2代表删除|
|tenantId|integer(int64)|false|none||租户编号|

<h2 id="tocS_CommonResultPageResultAiModelRespVO">CommonResultPageResultAiModelRespVO</h2>

<a id="schemacommonresultpageresultaimodelrespvo"></a>
<a id="schema_CommonResultPageResultAiModelRespVO"></a>
<a id="tocScommonresultpageresultaimodelrespvo"></a>
<a id="tocscommonresultpageresultaimodelrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 24150,
        "modelName": "张三",
        "modelFileId": "14429",
        "modelFileUrl": "http://baidu.com",
        "coverImageFileId": 14429,
        "coverImageFileUrl": 14429,
        "modelVideoFileId": "19647",
        "modelVideoFileUrl": "http://baidu.cc",
        "status": 0,
        "createTime": "string",
        "creator": "string",
        "updateTime": "string",
        "updater": "string",
        "deleted": true,
        "tenantId": 17682
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAiModelRespVO](#schemapageresultaimodelrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_PageResultAiModelRespVO">PageResultAiModelRespVO</h2>

<a id="schemapageresultaimodelrespvo"></a>
<a id="schema_PageResultAiModelRespVO"></a>
<a id="tocSpageresultaimodelrespvo"></a>
<a id="tocspageresultaimodelrespvo"></a>

```json
{
  "list": [
    {
      "id": 24150,
      "modelName": "张三",
      "modelFileId": "14429",
      "modelFileUrl": "http://baidu.com",
      "coverImageFileId": 14429,
      "coverImageFileUrl": 14429,
      "modelVideoFileId": "19647",
      "modelVideoFileUrl": "http://baidu.cc",
      "status": 0,
      "createTime": "string",
      "creator": "string",
      "updateTime": "string",
      "updater": "string",
      "deleted": true,
      "tenantId": 17682
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AiModelRespVO](#schemaaimodelrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_CommonResultAiModelRespVO">CommonResultAiModelRespVO</h2>

<a id="schemacommonresultaimodelrespvo"></a>
<a id="schema_CommonResultAiModelRespVO"></a>
<a id="tocScommonresultaimodelrespvo"></a>
<a id="tocscommonresultaimodelrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 24150,
    "modelName": "张三",
    "modelFileId": "14429",
    "modelFileUrl": "http://baidu.com",
    "coverImageFileId": 14429,
    "coverImageFileUrl": 14429,
    "modelVideoFileId": "19647",
    "modelVideoFileUrl": "http://baidu.cc",
    "status": 0,
    "createTime": "string",
    "creator": "string",
    "updateTime": "string",
    "updater": "string",
    "deleted": true,
    "tenantId": 17682
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AiModelRespVO](#schemaaimodelrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

