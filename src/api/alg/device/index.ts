import request from '@/config/axios'

// 创建视频流设备
export const createDevice = (data) => {
  return request.post({
    url: '/alg/resource-device/create',
    data
  })
}

// 更新视频流设备
export const updateDevice = (data) => {
  return request.put({
    url: '/alg/resource-device/update',
    data
  })
}

// 删除视频流设备
export const deleteDevice = (params) => {
  return request.delete({
    url: '/alg/resource-device/delete',
    params
  })
}

// 批量删除资源设备
export const deleteDeviceList = (params) => {
  return request.delete({
    url: '/alg/resource-device/delete-list',
    params
  })
}

// 获得视频流设备
export const getDevice = (params) => {
  return request.get({
    url: '/alg/resource-device/get',
    params
  })
}

// 获得视频流设备分页
export const getDevicePage = (params) => {
  return request.get({
    url: '/alg/resource-device/page',
    params
  })
}

// 导出视频流设备 Excel
export const exportDeviceExcel = (params) => {
  return request.download({
    url: '/alg/resource-device/export-excel',
    params
  })
}
