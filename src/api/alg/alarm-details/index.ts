import request from '@/config/axios'

// 创建告警明细
export const createAlarmDetails = (data) => {
  return request.post({
    url: '/alg/alarm-details/create',
    data
  })
}

// 更新告警明细
export const updateAlarmDetails = (data) => {
  return request.put({
    url: '/alg/alarm-details/update',
    data
  })
}

// 删除告警明细
export const deleteAlarmDetails = (params) => {
  return request.delete({
    url: '/alg/alarm-details/delete',
    params
  })
}

// 批量删除告警明细
export const deleteAlarmDetailsList = (params) => {
  return request.delete({
    url: '/alg/alarm-details/delete-list',
    params
  })
}

// 获得告警明细
export const getAlarmDetails = (params) => {
  return request.get({
    url: '/alg/alarm-details/get',
    params
  })
}

// 获得告警信息-明细分页
export const getAlarmDetailsPage = (params) => {
  return request.get({
    url: '/alg/alarm-details/page',
    params
  })
}

// 导出告警明细 Excel
export const exportAlarmDetailsExcel = (params) => {
  return request.download({
    url: '/alg/alarm-details/export-excel',
    params
  })
}

// 告警通知
export const notifyAlarm = (data) => {
  return request.post({
    url: '/alg/alarm-details/notify',
    data
  })
}
