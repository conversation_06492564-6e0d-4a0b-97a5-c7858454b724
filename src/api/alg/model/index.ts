import request from '@/config/axios'

// 创建模型
export const createModel = (data) => {
  return request.post({
    url: '/alg/ai-model/create',
    data
  })
}

// 更新模型
export const updateModel = (data) => {
  return request.put({
    url: '/alg/ai-model/update',
    data
  })
}

// 删除模型
export const deleteModel = (params) => {
  return request.delete({
    url: '/alg/ai-model/delete',
    params
  })
}

// 批量删除模型
export const deleteModelList = (params) => {
  return request.delete({
    url: '/alg/ai-model/delete-list',
    params
  })
}

// 获得模型
export const getModel = (params) => {
  return request.get({
    url: '/alg/ai-model/get',
    params
  })
}

// 获得模型分页
export const getModelPage = (params) => {
  return request.get({
    url: '/alg/ai-model/page',
    params
  })
}

// 导出模型 Excel
export const exportModelExcel = (params) => {
  return request.download({
    url: '/alg/ai-model/export-excel',
    params
  })
}
