import request from '@/config/axios'

// 创建任务
export const createTask = (data) => {
  return request.post({
    url: '/alg/task/create',
    data
  })
}

// 更新任务
export const updateTask = (data) => {
  return request.put({
    url: '/alg/task/update',
    data
  })
}

// 删除任务
export const deleteTask = (params) => {
  return request.delete({
    url: '/alg/task/delete',
    params
  })
}

// 批量删除任务
export const deleteTaskList = (params) => {
  return request.delete({
    url: '/alg/task/delete-list',
    params
  })
}

// 获得任务
export const getTask = (params) => {
  return request.get({
    url: '/alg/task/get',
    params
  })
}

// 获得任务分页
export const getTaskPage = (params) => {
  return request.get({
    url: '/alg/task/page',
    params
  })
}

// 导出任务 Excel
export const exportTaskExcel = (params) => {
  return request.download({
    url: '/alg/task/export-excel',
    params
  })
}

// 执行任务
export const startTask = (params) => {
  return request.get({
    url: '/alg/task/start-task',
    params
  })
}
