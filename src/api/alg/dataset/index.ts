import request from '@/config/axios'

// 创建数据集
export const createDataset = (data) => {
  return request.post({
    url: '/alg/dataset/create',
    data
  })
}

// 更新数据集
export const updateDataset = (data) => {
  return request.put({
    url: '/admin-api/alg/dataset/update',
    data
  })
}

// 删除数据集
export const deleteDataset = (params) => {
  return request.delete({
    url: '/alg/dataset/delete',
    params
  })
}

// 批量删除数据集
export const deleteDatasetList = (params) => {
  return request.delete({
    url: '/alg/dataset/delete-list',
    params
  })
}

// 获得数据集
export const getDataset = (params) => {
  return request.get({
    url: '/alg/dataset/get',
    params
  })
}

// 获得数据集分页
export const getDatasetPage = (params) => {
  return request.get({
    url: '/alg/dataset/page',
    params
  })
}

// 导出数据集 Excel
export const exportDatasetExcel = (params) => {
  return request.download({
    url: '/admin-api/alg/dataset/export-excel',
    params
  })
}
