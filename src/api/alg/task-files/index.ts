import request from '@/config/axios'

// 创建任务资源文件
export const createTaskFiles = (data) => {
  return request.post({
    url: '/alg/task-files/create',
    data
  })
}

// 更新任务资源文件
export const updateTaskFiles = (data) => {
  return request.put({
    url: '/alg/task-files/update',
    data
  })
}

// 删除任务资源文件
export const deleteTaskFiles = (params) => {
  return request.delete({
    url: '/alg/task-files/delete',
    params
  })
}

// 批量删除任务资源文件
export const deleteTaskFilesList = (params) => {
  return request.delete({
    url: '/alg/task-files/delete-list',
    params
  })
}

// 获得任务文件：视频、图片
export const getTaskFiles = (params) => {
  return request.get({
    url: '/alg/task-files/get',
    params
  })
}

// 获得任务任务资源文件分页
export const getTaskFilesPage = (params) => {
  return request.get({
    url: '/alg/task-files/page',
    params
  })
}

// 导出任务资源文件 Excel
export const exportTaskFilesExcel = (params) => {
  return request.download({
    url: '/alg/task-files/export-excel',
    params
  })
}
